import asyncio
import json
import re
import logging
import time
import traceback
from llm import LLM
from pprint import pprint
from prompts.constraint_prompt import get_constraint_prompt



class Constraint:
    def __init__(self, llm: LLM):
        self.llm = llm

    async def get_constraints(self, query: str, model="o4-mini"):
        prompt = get_constraint_prompt(query)
        messages = [
            # {"role": "system", "content": "You are an expert tool selector that selects the most relevant tools for a user's request."},
            {"role": "user", "content": prompt}
        ]
        # pprint(messages)
        # exit()
        start_time = time.time()
        response = await self.llm.generate(messages, model=model)
        end_time = time.time()
        print(f"Time taken: {end_time - start_time} seconds")
        return response


import matplotlib.pyplot as plt
import networkx as nx
from matplotlib.patches import Rectangle
import json
from typing import Dict, List, Any

class DAGVisualizer:
    """
    A class to visualize Directed Acyclic Graphs (DAGs) from task planning data.
    Creates interactive plots with legends and detailed task information.
    """
    
    def __init__(self):
        self.graph = nx.DiGraph()
        self.tasks = []
        self.color_map = {
            'Google Maps': '#4285F4',  # Google Blue
            'Walmart': '#004C91',      # Walmart Blue
            'Target': '#CC0000',       # Target Red
            'Aldi': '#FF6600',         # Aldi Orange
            'evaluate_data': '#28A745', # Green
            'select_data': '#6F42C1',   # Purple
            'generate_final_answers': '#FD7E14'  # Orange
        }
        
    def parse_tasks(self, task_data: Dict[str, Any]):
        """
        Parse task data and build the graph structure.
        
        Args:
            task_data: Dictionary containing 'tasks' key with list of task objects
        """
        self.tasks = json.loads(task_data).get('tasks', [])
        
        # Add nodes to graph
        for task in self.tasks:
            task_id = task['id']
            self.graph.add_node(task_id, **task)
            
        # Add edges based on preconditions
        for task in self.tasks:
            task_id = task['id']
            preconditions = task.get('preconditions', [])
            
            for precondition in preconditions:
                # Extract task ID from precondition string (e.g., "t1_find_walmart completed")
                if ' completed' in precondition:
                    dependency_id = precondition.replace(' completed', '')
                    if dependency_id in [t['id'] for t in self.tasks]:
                        self.graph.add_edge(dependency_id, task_id)
    
    def get_node_color(self, task_id: str) -> str:
        """
        Get color for a node based on its orchestration action.
        
        Args:
            task_id: The task identifier
            
        Returns:
            Color string for the node
        """
        task = next((t for t in self.tasks if t['id'] == task_id), None)
        if task:
            action = task.get('orchestration_action', 'default')
            return self.color_map.get(action, '#808080')  # Default gray
        return '#808080'
    
    def _create_hierarchical_layout(self):
        """
        Create a hierarchical layout with proper levels for parallel tasks.
        
        Returns:
            Dictionary of node positions
        """
        # Calculate levels for each node
        levels = {}
        execution_order = self.get_execution_order()
        
        for task_id in execution_order:
            # Calculate the maximum level of dependencies + 1
            max_dep_level = -1
            for pred in self.graph.predecessors(task_id):
                max_dep_level = max(max_dep_level, levels.get(pred, 0))
            levels[task_id] = max_dep_level + 1
        
        # Group tasks by level
        level_groups = {}
        for task_id, level in levels.items():
            if level not in level_groups:
                level_groups[level] = []
            level_groups[level].append(task_id)
        
        # Create positions
        pos = {}
        max_level = max(levels.values()) if levels else 0
        
        for level, tasks in level_groups.items():
            # Sort tasks within level for consistent positioning
            tasks.sort()
            num_tasks = len(tasks)
            
            # Calculate y position (top to bottom)
            y = max_level - level
            
            # Calculate x positions to center the group
            if num_tasks == 1:
                x_positions = [0]
            else:
                # Spread tasks horizontally
                width = min(8, num_tasks * 1.5)  # Limit maximum width
                x_positions = [i * width / (num_tasks - 1) - width/2 for i in range(num_tasks)]
            
            for i, task_id in enumerate(tasks):
                pos[task_id] = (x_positions[i], y * 2)  # Multiply by 2 for better spacing
        
        return pos
    
    def _add_level_indicators(self, ax, pos):
        """
        Add level indicators to show execution phases.
        
        Args:
            ax: Matplotlib axis object
            pos: Node positions dictionary
        """
        # Get unique y levels
        y_levels = sorted(set(y for x, y in pos.values()), reverse=True)
        
        # Add level labels
        for i, y_level in enumerate(y_levels):
            ax.text(-6, y_level, f'Level {i+1}', 
                   fontsize=12, fontweight='bold', 
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.7),
                   verticalalignment='center')
        
        # Add horizontal lines to separate levels
        x_min = min(x for x, y in pos.values()) - 1
        x_max = max(x for x, y in pos.values()) + 1
        
        for y_level in y_levels[:-1]:  # Don't draw line after last level
            ax.axhline(y=y_level - 1, xmin=0.1, xmax=0.9, 
                      color='lightgray', linestyle='--', alpha=0.5, linewidth=1)
    
    def create_legend(self, ax):
        """
        Create a legend showing orchestration actions and their colors.
        
        Args:
            ax: Matplotlib axis object
        """
        # Get unique orchestration actions from tasks
        actions = set(task.get('orchestration_action', 'unknown') for task in self.tasks)
        
        legend_elements = []
        for action in sorted(actions):
            color = self.color_map.get(action, '#808080')
            legend_elements.append(Rectangle((0, 0), 1, 1, facecolor=color, label=action))
        
        ax.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(1.05, 1))
    
    def plot_dag(self, figsize=(20, 14), save_path=None, show_details=True, verifier_feedback=None, user_input=None):
        """
        Create and display the DAG visualization.

        Args:
            figsize: Figure size tuple (width, height)
            save_path: Optional path to save the figure
            show_details: Whether to show detailed task information
        """
        if not self.tasks:
            print("No tasks to visualize. Please call parse_tasks() first.")
            return
        
        fig = plt.figure(figsize=figsize)
        
        # Add task details and get the main axis if requested
        if show_details:
            ax = self.add_task_details(fig)
        else:
            ax = fig.add_subplot(111)
        
        # Create hierarchical layout with proper levels
        pos = self._create_hierarchical_layout()
        
        # Get node colors
        node_colors = [self.get_node_color(node) for node in self.graph.nodes()]
        
        # Draw the graph with improved styling
        nx.draw_networkx_nodes(self.graph, pos, node_color=node_colors, 
                              node_size=4000, alpha=0.9, ax=ax, 
                              edgecolors='black', linewidths=2)
        
        # Draw edges with much better visibility and clearer arrows
        nx.draw_networkx_edges(self.graph, pos, edge_color='#000000', 
                              arrows=True, arrowsize=30, arrowstyle='-|>', 
                              alpha=1.0, width=3, ax=ax, connectionstyle='arc3,rad=0.1')
        
        # Add improved labels
        labels = {node: node.replace('_', '\n').replace('t', 'T') for node in self.graph.nodes()}
        nx.draw_networkx_labels(self.graph, pos, labels, font_size=9, 
                               font_weight='bold', ax=ax, font_color='white')
        
        # Add level indicators
        self._add_level_indicators(ax, pos)
        
        # Create legend
        self.create_legend(ax)
        
        # Add subtitle with actual verifier feedback and user input
        if verifier_feedback and user_input:
            score = verifier_feedback.get('score', 'N/A')
            plan_feedback = verifier_feedback.get('plan_feedback', 'No feedback available')[:80] + '...' if len(verifier_feedback.get('plan_feedback', '')) > 80 else verifier_feedback.get('plan_feedback', 'No feedback available')
            user_query = user_input[:60] + '...' if len(user_input) > 60 else user_input
            
            subtitle = f'Query: {user_query}\nVerifier Score: {score}/100 | Feedback: {plan_feedback}'
        else:
            subtitle = 'Grocery Shopping Plan - Optimized Execution Flow\nVerifier: All dependencies validated | Reward: High efficiency | Status: Ready for execution'
        
        ax.text(0.5, 0.02, subtitle, transform=ax.transAxes, fontsize=9, 
               ha='center', va='bottom', style='italic', alpha=0.8,
               bbox=dict(boxstyle="round,pad=0.5", facecolor="#f0f8ff", alpha=0.7))
        ax.axis('off')
        
        # Adjust layout based on whether details are shown
        if show_details:
            plt.subplots_adjust(left=0.05, right=0.95, top=0.9, bottom=0.1)
        else:
            plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"DAG visualization saved to: {save_path}")
        
        plt.show()
    
    def add_task_details(self, fig):
        """
        Add detailed task information in a separate subplot.

        Args:
            fig: Matplotlib figure object
        """
        # Create a separate subplot for task details
        gs = fig.add_gridspec(1, 2, width_ratios=[3, 1], hspace=0.1)
        
        # Move the main plot to the left subplot
        main_ax = fig.add_subplot(gs[0])
        details_ax = fig.add_subplot(gs[1])
        
        # Clear the details axis and hide its frame
        details_ax.axis('off')
        
        # Prepare task details text
        details_lines = []
        details_lines.append("[*] TASK DETAILS")
        details_lines.append("=" * 25)
        details_lines.append("")
        
        for i, task in enumerate(self.tasks, 1):
            details_lines.append(f"{i:2d}. {task['id']}")
            details_lines.append(f"   {task['name'][:35]}{'...' if len(task['name']) > 35 else ''}")
            details_lines.append(f"   Action: {task['orchestration_action']}")
            
            if task.get('preconditions'):
                deps = ', '.join([dep.replace(' completed', '') for dep in task['preconditions']])
                details_lines.append(f"   Deps: {deps[:25]}{'...' if len(deps) > 25 else ''}")
            
            details_lines.append("")
        
        # Add scrollable note
        details_lines.append("[Scroll for more details]")
        
        # Add the text to the details subplot positioned much lower to avoid legend overlap
        details_text = '\n'.join(details_lines)
        details_ax.text(0.02, 0.65, details_text, fontsize=7, fontfamily='monospace',
                       verticalalignment='top', horizontalalignment='left',
                       transform=details_ax.transAxes,
                       bbox=dict(boxstyle='round,pad=0.8', facecolor='#f8f9fa', 
                                edgecolor='#dee2e6', alpha=0.95, linewidth=1),
                       wrap=True)
        
        # Add a title for the details panel positioned lower
        details_ax.text(0.5, 0.75, "Task Information", transform=details_ax.transAxes,
                       fontsize=10, fontweight='bold', ha='center',
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="#e9ecef", alpha=0.8))
        
        return main_ax
    
    def get_execution_order(self) -> List[str]:
        """
        Get the topological order of task execution.
        
        Returns:
            List of task IDs in execution order
        """
        try:
            return list(nx.topological_sort(self.graph))
        except nx.NetworkXError:
            print("Warning: Graph contains cycles!")
            return list(self.graph.nodes())
    
    def analyze_dag(self) -> Dict[str, Any]:
        """
        Analyze the DAG and return statistics.
        
        Returns:
            Dictionary with DAG analysis results
        """
        analysis = {
            'total_tasks': len(self.tasks),
            'total_edges': len(self.graph.edges()),
            'is_dag': nx.is_directed_acyclic_graph(self.graph),
            'execution_order': self.get_execution_order(),
            'parallel_groups': [],
            'orchestration_actions': {}
        }
        
        # Count orchestration actions
        for task in self.tasks:
            action = task.get('orchestration_action', 'unknown')
            analysis['orchestration_actions'][action] = analysis['orchestration_actions'].get(action, 0) + 1
        
        # Find parallel execution groups
        execution_order = analysis['execution_order']
        levels = {}
        
        for task_id in execution_order:
            # Calculate the maximum level of dependencies + 1
            max_dep_level = -1
            for pred in self.graph.predecessors(task_id):
                max_dep_level = max(max_dep_level, levels.get(pred, 0))
            levels[task_id] = max_dep_level + 1
        
        # Group tasks by level (can execute in parallel)
        level_groups = {}
        for task_id, level in levels.items():
            if level not in level_groups:
                level_groups[level] = []
            level_groups[level].append(task_id)
        
        # Only include groups with more than 1 task as parallel groups
        analysis['parallel_groups'] = []
        for level, group in level_groups.items():
            if len(group) > 1:
                analysis['parallel_groups'].append(group)
        
        analysis['level_groups'] = level_groups
        
        return analysis
    
    def print_analysis(self):
        """
        Print a detailed analysis of the DAG.
        """
        analysis = self.analyze_dag()
        
        print("\n" + "="*60)
        print("DAG ANALYSIS REPORT")
        print("="*60)
        
        print(f"Total Tasks: {analysis['total_tasks']}")
        print(f"Total Dependencies: {analysis['total_edges']}")
        print(f"Is Valid DAG: {analysis['is_dag']}")
        
        print("\nOrchestration Actions:")
        for action, count in analysis['orchestration_actions'].items():
            print(f"  {action}: {count} tasks")
        
        print("\nExecution Levels (Top to Bottom):")
        level_groups = analysis.get('level_groups', {})
        for level in sorted(level_groups.keys()):
            tasks = level_groups[level]
            if len(tasks) > 1:
                print(f"  Level {level + 1}: {', '.join(sorted(tasks))} (Parallel)")
            else:
                print(f"  Level {level + 1}: {tasks[0]}")
        
        print("\nSequential Execution Order:")
        for i, task_id in enumerate(analysis['execution_order'], 1):
            print(f"  {i:2d}. {task_id}")
        
        if analysis['parallel_groups']:
            print("\nParallel Execution Opportunities:")
            for i, group in enumerate(analysis['parallel_groups'], 1):
                print(f"  Level {i}: {', '.join(sorted(group))} ({len(group)} tasks can run in parallel)")
        else:
            print("\nNo parallel execution opportunities found.")


def example_usage():
    """
    Example usage of the DAG visualizer with sample data.
    """
    # Sample task data for demonstration
    sample_data = {
        'tasks': [
            {
                'id': 't1_find_walmart',
                'name': 'Find Walmart stores within 5 miles',
                'orchestration_action': 'Google Maps',
                'preconditions': []
            },
            {
                'id': 't2_find_target',
                'name': 'Find Target stores within 5 miles', 
                'orchestration_action': 'Google Maps',
                'preconditions': []
            },
            {
                'id': 't3_search_walmart_shrimp',
                'name': 'Search for shrimp at Walmart',
                'orchestration_action': 'Walmart',
                'preconditions': ['t1_find_walmart completed']
            },
            {
                'id': 't4_search_target_shrimp',
                'name': 'Search for shrimp at Target',
                'orchestration_action': 'Target', 
                'preconditions': ['t2_find_target completed']
            },
            {
                'id': 't5_evaluate_stores',
                'name': 'Evaluate store options',
                'orchestration_action': 'evaluate_data',
                'preconditions': ['t3_search_walmart_shrimp completed', 't4_search_target_shrimp completed']
            },
            {
                'id': 't6_select_best_store',
                'name': 'Select the best store option',
                'orchestration_action': 'select_data',
                'preconditions': ['t5_evaluate_stores completed']
            }
        ]
    }
    
    # Create and demonstrate the visualizer
    visualizer = DAGVisualizer()
    visualizer.parse_tasks(sample_data)
    
    print("\n" + "="*50)
    print("DAG VISUALIZER EXAMPLE")
    print("="*50)
    
    # Print analysis
    visualizer.print_analysis()
    
    # Create visualization
    print("\nGenerating DAG visualization...")
    visualizer.plot_dag(save_path='example_dag.png')
    
    return visualizer

if __name__ == "__main__":
    example_usage()
{'tasks': [{'id': 't1_search_cities',
            'name': 'Search for top tourist cities in Thailand for October '
                    '2025',
            'orchestration_action': 'Search Engines (SERP)',
            'parameters': {'query': 'top tourist cities in Thailand October '
                                    '2025'},
            'preconditions': [],
            'type': 'primitive'},
           {'id': 't2_rank_cities',
            'name': 'Rank cities to select top 3',
            'orchestration_action': 'rank_data',
            'parameters': {'criteria': {'activity_availability': 'high',
                                        'safety': 'high',
                                        'tourist_appeal': 'high'},
                           'input_data': 'results_of_t1_search_cities',
                           'prompt': 'From the web search results in '
                                     "'results_of_t1_search_cities', identify "
                                     'all mentioned Thai cities and rank them '
                                     'by overall tourist appeal, safety, and '
                                     'activity availability for a 30‑day '
                                     'October trip.'},
            'preconditions': ['t1_search_cities completed'],
            'type': 'primitive'},
           {'id': 't3_select_cities',
            'name': 'Select top 3 cities for the trip',
            'orchestration_action': 'select_data',
            'parameters': {'criteria': {'topk': 3},
                           'explain': True,
                           'options': 'results_of_t2_rank_cities',
                           'prompt': 'Select the three best cities for a '
                                     '30‑day October trip from the ranked '
                                     'list.',
                           'topk': 3},
            'preconditions': ['t2_rank_cities completed'],
            'type': 'primitive'},
           {'id': 't4_search_hotels_city1',
            'name': 'Search hotels in city 1',
            'orchestration_action': 'Google Hotels',
            'parameters': {'adults': 1,
                           'check_in': '2025-10-01',
                           'check_out': '2025-10-10',
                           'currency': 'USD',
                           'destination': 'results_of_t3_select_cities.data.selected_cities[0]'},
            'preconditions': ['t3_select_cities completed'],
            'type': 'primitive'},
           {'id': 't5_select_hotel_city1',
            'name': 'Select best hotel in city 1',
            'orchestration_action': 'select_data',
            'parameters': {'criteria': {'location_preference': 'city_center',
                                        'min_rating': 4.0,
                                        'price_currency': 'USD'},
                           'explain': True,
                           'options': 'results_of_t4_search_hotels_city1',
                           'prompt': 'Select the best hotel for a 1‑adult '
                                     'traveler in '
                                     'results_of_t3_select_cities.data.selected_cities[0] '
                                     'based on high rating (>=4.0), price in '
                                     'USD, and central location.',
                           'topk': 1},
            'preconditions': ['t4_search_hotels_city1 completed'],
            'type': 'primitive'},
           {'id': 't6_search_activities_city1',
            'name': 'Search activities near selected hotel in city 1',
            'orchestration_action': 'Search Engines (SERP)',
            'parameters': {'query': 'popular activities near '
                                    'results_of_t5_select_hotel_city1.data.selected_options[0].option.name '
                                    'results_of_t3_select_cities.data.selected_cities[0]'},
            'preconditions': ['t5_select_hotel_city1 completed'],
            'type': 'primitive'},
           {'id': 't7_select_activity_city1',
            'name': 'Select best activity near hotel in city 1',
            'orchestration_action': 'select_data',
            'parameters': {'criteria': {'popularity': 'high',
                                        'suitable_weather': 'October'},
                           'explain': True,
                           'options': 'results_of_t6_search_activities_city1',
                           'prompt': 'From the list of activities near the '
                                     'selected hotel, choose the best activity '
                                     'suitable for October weather, '
                                     'indoor/outdoor mix.',
                           'topk': 1},
            'preconditions': ['t6_search_activities_city1 completed'],
            'type': 'primitive'},
           {'id': 't8_search_hotels_city2',
            'name': 'Search hotels in city 2',
            'orchestration_action': 'Google Hotels',
            'parameters': {'adults': 1,
                           'check_in': '2025-10-11',
                           'check_out': '2025-10-20',
                           'currency': 'USD',
                           'destination': 'results_of_t3_select_cities.data.selected_cities[1]'},
            'preconditions': ['t3_select_cities completed'],
            'type': 'primitive'},
           {'id': 't9_select_hotel_city2',
            'name': 'Select best hotel in city 2',
            'orchestration_action': 'select_data',
            'parameters': {'criteria': {'location_preference': 'city_center',
                                        'min_rating': 4.0,
                                        'price_currency': 'USD'},
                           'explain': True,
                           'options': 'results_of_t8_search_hotels_city2',
                           'prompt': 'Select the best hotel for a 1‑adult '
                                     'traveler in '
                                     'results_of_t3_select_cities.data.selected_cities[1] '
                                     'based on high rating (>=4.0), price in '
                                     'USD, and central location.',
                           'topk': 1},
            'preconditions': ['t8_search_hotels_city2 completed'],
            'type': 'primitive'},
           {'id': 't10_search_activities_city2',
            'name': 'Search activities near selected hotel in city 2',
            'orchestration_action': 'Search Engines (SERP)',
            'parameters': {'query': 'popular activities near '
                                    'results_of_t9_select_hotel_city2.data.selected_options[0].option.name '
                                    'results_of_t3_select_cities.data.selected_cities[1]'},
            'preconditions': ['t9_select_hotel_city2 completed'],
            'type': 'primitive'},
           {'id': 't11_select_activity_city2',
            'name': 'Select best activity near hotel in city 2',
            'orchestration_action': 'select_data',
            'parameters': {'criteria': {'popularity': 'high',
                                        'suitable_weather': 'October'},
                           'explain': True,
                           'options': 'results_of_t10_search_activities_city2',
                           'prompt': 'From the list of activities near the '
                                     'selected hotel, choose the best activity '
                                     'suitable for October weather.',
                           'topk': 1},
            'preconditions': ['t10_search_activities_city2 completed'],
            'type': 'primitive'},
           {'id': 't12_search_hotels_city3',
            'name': 'Search hotels in city 3',
            'orchestration_action': 'Google Hotels',
            'parameters': {'adults': 1,
                           'check_in': '2025-10-21',
                           'check_out': '2025-10-30',
                           'currency': 'USD',
                           'destination': 'results_of_t3_select_cities.data.selected_cities[2]'},
            'preconditions': ['t3_select_cities completed'],
            'type': 'primitive'},
           {'id': 't13_select_hotel_city3',
            'name': 'Select best hotel in city 3',
            'orchestration_action': 'select_data',
            'parameters': {'criteria': {'location_preference': 'city_center',
                                        'min_rating': 4.0,
                                        'price_currency': 'USD'},
                           'explain': True,
                           'options': 'results_of_t12_search_hotels_city3',
                           'prompt': 'Select the best hotel for a 1‑adult '
                                     'traveler in '
                                     'results_of_t3_select_cities.data.selected_cities[2] '
                                     'based on high rating (>=4.0), price in '
                                     'USD, and central location.',
                           'topk': 1},
            'preconditions': ['t12_search_hotels_city3 completed'],
            'type': 'primitive'},
           {'id': 't14_search_activities_city3',
            'name': 'Search activities near selected hotel in city 3',
            'orchestration_action': 'Search Engines (SERP)',
            'parameters': {'query': 'popular activities near '
                                    'results_of_t13_select_hotel_city3.data.selected_options[0].option.name '
                                    'results_of_t3_select_cities.data.selected_cities[2]'},
            'preconditions': ['t13_select_hotel_city3 completed'],
            'type': 'primitive'},
           {'id': 't15_select_activity_city3',
            'name': 'Select best activity near hotel in city 3',
            'orchestration_action': 'select_data',
            'parameters': {'criteria': {'popularity': 'high',
                                        'suitable_weather': 'October'},
                           'explain': True,
                           'options': 'results_of_t14_search_activities_city3',
                           'prompt': 'From the list of activities near the '
                                     'selected hotel, choose the best activity '
                                     'suitable for October weather.',
                           'topk': 1},
            'preconditions': ['t14_search_activities_city3 completed'],
            'type': 'primitive'},
           {'id': 't16_flight_city1_to_city2',
            'name': 'Search flight from city 1 to city 2',
            'orchestration_action': 'Google Flights',
            'parameters': {'adults': 1,
                           'currency': 'USD',
                           'date': '2025-10-11',
                           'destination': 'results_of_t3_select_cities.data.selected_cities[1]',
                           'origin': 'results_of_t3_select_cities.data.selected_cities[0]'},
            'preconditions': ['t5_select_hotel_city1 completed',
                              't9_select_hotel_city2 completed'],
            'type': 'primitive'},
           {'id': 't17_flight_city2_to_city3',
            'name': 'Search flight from city 2 to city 3',
            'orchestration_action': 'Google Flights',
            'parameters': {'adults': 1,
                           'currency': 'USD',
                           'date': '2025-10-21',
                           'destination': 'results_of_t3_select_cities.data.selected_cities[2]',
                           'origin': 'results_of_t3_select_cities.data.selected_cities[1]'},
            'preconditions': ['t9_select_hotel_city2 completed',
                              't13_select_hotel_city3 completed'],
            'type': 'primitive'},
           {'id': 't18_generate_final_plan',
            'name': 'Generate final trip plan',
            'orchestration_action': 'generate_final_answers',
            'parameters': {'format': 'markdown',
                           'original_question': 'plan my trip to thiland, find '
                                                'the top 3 cities and find me '
                                                'the best hotel in each city '
                                                'also find the best activity '
                                                'nearby the selected hotel for '
                                                'each city, plan for oct 1 to '
                                                'oct 30 2025 for 1 adult '
                                                'please use USD',
                           'processed_data': 'results_of_t15_select_activity_city3'},
            'preconditions': ['t7_select_activity_city1 completed',
                              't11_select_activity_city2 completed',
                              't15_select_activity_city3 completed',
                              't16_flight_city1_to_city2 completed',
                              't17_flight_city2_to_city3 completed'],
            'type': 'primitive'}]}
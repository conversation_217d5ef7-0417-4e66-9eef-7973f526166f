"""
Main LLM class providing a unified interface for different LLM providers.
"""
import os
import logging
from typing import Dict, List, Optional, Union, Any, AsyncGenerator

from llm.base import LL<PERSON>rovider
from llm.openai_provider import OpenAIProvider
from llm.deepseek_provider import DeepseekProvider
from llm.minimax_provider import MinimaxProvider
from llm.gemini_provider import GeminiProvider
from llm.openrouter_provider import OpenRouterProvider


class LLM:
    """
    LLM abstraction class that routes requests to the appropriate provider.

    Serves as a factory for creating and managing different LLM providers,
    providing a unified interface for text generation and embeddings.
    """

    PROVIDER_MAPPING = {
        "openai": OpenAIProvider,
        "deepseek": DeepseekProvider,
        "minimax": MinimaxProvider,
        "gemini": GeminiProvider,
        "openrouter": OpenRouterProvider
    }

    # Models that use max_completion_tokens instead of max_tokens or require special handling
    THINKING_MODELS = {
        "openai": ["o3-mini", "o1-mini", "o1", "o4-mini", "o3"],
        "deepseek": ["deepseek-reasoner"],
        "gemini": ["gemini-2.5-pro-preview", "gemini-1.5-pro", "gemini-1.5-flash"],  # Gemini models need special handling
        "openrouter": ["google/gemini-2.5-pro-preview", "google/gemini-1.5-pro", "google/gemini-1.5-flash"]  # OpenRouter with Gemini models
    }

    @classmethod
    def get_provider_from_model(cls, model: str) -> str:
        """
        Determine the appropriate provider based on model name.

        Args:
            model: The model name to analyze

        Returns:
            Provider name as a string
        """
        model_lower = model.lower()

        # Check for OpenRouter models (contain a slash)
        if '/' in model_lower:
            return "openrouter"

        # Check for OpenAI models
        if (model_lower.startswith('gpt') or
            model_lower.startswith('o') or  # All o-series models (o1, o3, o4, etc.)
            model_lower in ['o1', 'o3', 'o4', 'o1-mini', 'o3-mini', 'o4-mini']):
            return "openai"

        # Check for Gemini models
        if model_lower.startswith('gemini'):
            return "gemini"

        # Check for MiniMax models
        if model_lower == "minimax-text-01":
            return "minimax"

        # Deepseek models (note: OpenRouter takes priority if there's a slash)
        if model_lower.startswith('deepseek'):
            return "deepseek"

        # Default to OpenAI if we can't determine the provider
        logging.warning(f"Could not determine provider for model '{model}'. Defaulting to OpenAI.")
        return "openai"

    def __init__(
        self,
        provider: str = "openai",
        api_key: Optional[str] = None,
        model_mapping: Optional[Dict[str, str]] = None,
        **kwargs
    ):
        """
        Initialize the LLM with a specific provider.

        Args:
            provider: The provider to use ("openai", "deepseek", "minimax", "gemini", or "openrouter")
            api_key: API key for the provider (defaults to environment variable)
            model_mapping: Custom mapping of generic model names to provider-specific models
            **kwargs: Additional provider-specific initialization parameters
        """
        self.provider_name = provider.lower()

        if self.provider_name not in self.PROVIDER_MAPPING:
            raise ValueError(f"Unsupported provider: {provider}. Must be one of: {', '.join(self.PROVIDER_MAPPING.keys())}")

        # Initialize the provider
        provider_class = self.PROVIDER_MAPPING[self.provider_name]
        self.provider = provider_class(api_key=api_key, **kwargs)

        # Default model mappings
        self.default_model_mapping = {
            # Text generation models
            "default": {
                "openai": "gpt-4o",
                "deepseek": "deepseek-chat",
                "minimax": "MiniMax-Text-01",
                "gemini": "gemini-1.5-pro",
                "openrouter": "openai/gpt-4o"
            },
            "fast": {
                "openai": "gpt-4o-mini",
                "deepseek": "deepseek-chat",
                "minimax": "MiniMax-Text-01",
                "gemini": "gemini-1.5-flash",
                "openrouter": "anthropic/claude-3-haiku"
            },
            "powerful": {
                "openai": "o3-mini",
                "deepseek": "deepseek-reasoner",
                "minimax": "MiniMax-Text-01",
                "gemini": "gemini-1.5-pro",
                "openrouter": "anthropic/claude-3-opus"
            },
            # Embedding models
            "embedding": {
                "openai": "text-embedding-3-small",
                "gemini": "embedding-001",
                "openrouter": "openai/text-embedding-3-large"
            }
        }

        # Update with custom model mapping if provided
        if model_mapping:
            for category, mapping in model_mapping.items():
                if category in self.default_model_mapping:
                    self.default_model_mapping[category].update(mapping)
                else:
                    self.default_model_mapping[category] = mapping

    def _resolve_model(self, model: str) -> str:
        """
        Resolve a generic model name to a provider-specific model name.

        Args:
            model: Generic model name or category

        Returns:
            Provider-specific model name
        """
        # If the model is a category in our mapping, use the mapped model
        if model in self.default_model_mapping:
            return self.default_model_mapping[model].get(self.provider_name, model)

        # Otherwise, use the model name as is
        return model

    def _is_thinking_model(self, model: str) -> bool:
        """
        Check if a model is a thinking model that uses max_completion_tokens.

        Args:
            model: The model name to check

        Returns:
            True if the model is a thinking model, False otherwise
        """
        thinking_models = self.THINKING_MODELS.get(self.provider_name, [])
        return model in thinking_models

    def _prepare_generation_params(self, model: str, max_tokens: Optional[int], temperature: float = 0.0, **kwargs) -> Dict[str, Any]:
        """
        Prepare parameters for generation based on the model type.

        Args:
            model: The provider-specific model name
            max_tokens: Maximum number of tokens to generate
            temperature: Controls randomness (0.0 to 1.0)
            **kwargs: Additional generation parameters

        Returns:
            Dictionary of generation parameters with the correct token limit parameter
        """
        params = kwargs.copy()

        # Remove 'provider' parameter if it exists to avoid passing it to the API
        if 'provider' in params:
            del params['provider']

        # Handle thinking models differently
        if self._is_thinking_model(model):
            logging.info(f"Setting up parameters for thinking model: {model}")

            # For thinking models: Use max_completion_tokens and add reasoning_effort
            if max_tokens is not None:
                logging.info(f"Using max_completion_tokens={max_tokens} for thinking model: {model}")
                params["max_completion_tokens"] = max_tokens

            # Do not set temperature for thinking models, use reasoning_effort instead
            if "reasoning_effort" not in params:
                params["reasoning_effort"] = "high"
                logging.info(f"Setting reasoning_effort=high for thinking model: {model}")

            # Make sure temperature is not included for thinking models
            if "temperature" in params:
                logging.info(f"Removing temperature parameter for thinking model: {model}")
                del params["temperature"]
        else:
            # For regular models: Use max_tokens and temperature
            if max_tokens is not None:
                params["max_tokens"] = max_tokens

            # Add temperature for non-thinking models
            if "temperature" not in params:
                params["temperature"] = temperature

        return params

    async def generate(
        self,
        messages: List[Dict[str, str]],
        model: str = "default",
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> str:
        """
        Generate a response using the configured provider.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            model: Model identifier or category ("default", "fast", "powerful")
            temperature: Controls randomness (0.0 to 1.0, ignored for thinking models)
            max_tokens: Maximum number of tokens to generate
            **kwargs: Additional provider-specific parameters

        Returns:
            Generated text response
        """
        # If a specific model was provided (not a category), check if we need to use a different provider
        if model not in self.default_model_mapping:
            model_provider = self.get_provider_from_model(model)
            if model_provider != self.provider_name:
                logging.info(f"Auto-switching provider from {self.provider_name} to {model_provider} for model {model}")
                # Create a new LLM instance with the correct provider and delegate the call
                temp_llm = LLM(provider=model_provider)
                return await temp_llm.generate(
                    messages=messages,
                    model=model,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    **kwargs
                )

        provider_model = self._resolve_model(model)

        # Prepare parameters based on model type
        params = self._prepare_generation_params(provider_model, max_tokens, temperature, **kwargs)

        return await self.provider.generate(
            messages=messages,
            model=provider_model,
            **params
        )

    async def stream_generate(
        self,
        messages: List[Dict[str, str]],
        model: str = "default",
        temperature: float = 0.0,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        Generate a streaming response using the configured provider.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            model: Model identifier or category ("default", "fast", "powerful")
            temperature: Controls randomness (0.0 to 1.0, ignored for thinking models)
            max_tokens: Maximum number of tokens to generate
            **kwargs: Additional provider-specific parameters

        Returns:
            AsyncGenerator yielding chunks of the response as they become available
        """
        # If a specific model was provided (not a category), check if we need to use a different provider
        if model not in self.default_model_mapping:
            model_provider = self.get_provider_from_model(model)
            if model_provider != self.provider_name:
                logging.info(f"Auto-switching provider from {self.provider_name} to {model_provider} for model {model}")
                # Create a new LLM instance with the correct provider and delegate the call
                temp_llm = LLM(provider=model_provider)
                async for chunk in temp_llm.stream_generate(
                    messages=messages,
                    model=model,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    **kwargs
                ):
                    yield chunk
                return

        provider_model = self._resolve_model(model)

        # Prepare parameters based on model type
        params = self._prepare_generation_params(provider_model, max_tokens, temperature, **kwargs)

        async for chunk in self.provider.stream_generate(
            messages=messages,
            model=provider_model,
            **params
        ):
            yield chunk

    async def embed(self, text: str, model: str = "embedding") -> List[float]:
        """
        Generate embeddings using the configured provider.

        Args:
            text: The text to embed
            model: Model identifier or category ("embedding")

        Returns:
            List of floating point values representing the embedding
        """
        # If a specific model was provided (not a category), check if we need to use a different provider
        if model not in self.default_model_mapping:
            model_provider = self.get_provider_from_model(model)
            if model_provider != self.provider_name:
                logging.info(f"Auto-switching provider from {self.provider_name} to {model_provider} for model {model}")
                # Create a new LLM instance with the correct provider and delegate the call
                temp_llm = LLM(provider=model_provider)
                return await temp_llm.embed(text=text, model=model)

        provider_model = self._resolve_model(model)

        return await self.provider.embed(
            text=text,
            model=provider_model
        )

    @classmethod
    def create(cls, provider: str = "auto", model: Optional[str] = None, **kwargs) -> 'LLM':
        """
        Factory method to create an LLM instance with the appropriate provider.

        Args:
            provider: The provider to use, or "auto" to detect from model
            model: Model name to use for auto-detection
            **kwargs: Additional parameters to pass to the LLM constructor

        Returns:
            LLM instance
        """
        if provider == "auto" and model:
            provider = cls.get_provider_from_model(model)
            logging.info(f"Auto-detected provider {provider} for model {model}")
        elif provider == "auto":
            provider = "openai"  # Default if no model specified

        return cls(provider=provider, **kwargs)
"""
Minimax LLM provider implementation.
"""
import os
import json
import requests
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator
from openai import Async<PERSON>penAI
from llm.base import <PERSON><PERSON>rovider


class MinimaxProvider(LLMProvider):
    """
    Minimax API provider implementation.

    Handles communication with Minimax's API for text generation.
    Supports both AsyncOpenAI client and direct REST API approaches.
    """

    def __init__(self, api_key: Optional[str] = None, use_openai_client: bool = True):
        """
        Initialize the Minimax provider.

        Args:
            api_key: Minimax API key (defaults to MINIMAX_API_KEY environment variable)
            use_openai_client: Whether to use AsyncOpenAI client (True) or direct REST API (False)
        """
        self.api_key = api_key or os.getenv("MINIMAX_API_KEY")
        if not self.api_key:
            raise ValueError("Minimax API key is required")

        self.use_openai_client = use_openai_client

        if use_openai_client:
            self.client = AsyncOpenAI(
                api_key=self.api_key,
                base_url="https://api.minimaxi.chat/v1"
            )

    async def generate(
        self,
        messages: List[Dict[str, str]],
        model: str = "MiniMax-Text-01",
        temperature: Optional[float] = 0.0,
        max_tokens: Optional[int] = None,
        max_completion_tokens: Optional[int] = None,
        reasoning_effort: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Generate a response using Minimax's API.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            model: Minimax model identifier (default: MiniMax-Text-01)
            temperature: Controls randomness (0.0 to 1.0)
            max_tokens: Maximum number of tokens to generate (for non-thinking models)
            max_completion_tokens: Maximum number of tokens to generate (for thinking models)
            reasoning_effort: Not used for Minimax models
            **kwargs: Additional API parameters

        Returns:
            Generated text response
        """
        if self.use_openai_client:
            # Use AsyncOpenAI client with Minimax base URL
            params = {
                "model": model,
                "messages": messages,
            }

            # MiniMax uses temperature parameter
            if temperature is not None:
                params["temperature"] = temperature

            # MiniMax only supports max_tokens
            if max_tokens is not None:
                params["max_tokens"] = max_tokens
                logging.info(f"Using max_tokens={max_tokens} for MiniMax model")
            elif max_completion_tokens is not None:
                # Use max_completion_tokens as max_tokens if provided
                params["max_tokens"] = max_completion_tokens
                logging.info(f"Using max_completion_tokens={max_completion_tokens} as max_tokens for MiniMax model")

            # Add any additional parameters
            params.update(kwargs)

            response = await self.client.chat.completions.create(**params)
            return response.choices[0].message.content
        else:
            # Use direct REST API (simplified for brevity)
            # MiniMax API implementation would go here
            raise NotImplementedError("Direct REST API implementation is not currently supported")

    async def stream_generate(
        self,
        messages: List[Dict[str, str]],
        model: str = "MiniMax-Text-01",
        temperature: Optional[float] = 0.0,
        max_tokens: Optional[int] = None,
        max_completion_tokens: Optional[int] = None,
        reasoning_effort: Optional[str] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        Stream a response using OpenAI's API as a proxy for MiniMax.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            model: MiniMax model identifier (default: MiniMax-Text-01)
            temperature: Controls randomness (0.0 to 1.0)
            max_tokens: Maximum number of tokens to generate (for non-thinking models)
            max_completion_tokens: Maximum number of tokens to generate (for thinking models)
            reasoning_effort: Not used for Minimax models
            **kwargs: Additional API parameters

        Returns:
            AsyncGenerator yielding chunks of the response as they become available
        """
        logging.info(f"Starting MiniMax streaming via OpenAI API with model: {model}")

        params = {
            "model": model,
            "messages": messages,
            "stream": True,
        }

        # MiniMax uses temperature parameter
        if temperature is not None:
            params["temperature"] = temperature

        # MiniMax only supports max_tokens
        if max_tokens is not None:
            params["max_tokens"] = max_tokens
            logging.info(f"Using max_tokens={max_tokens} for MiniMax model")
        elif max_completion_tokens is not None:
            # Use max_completion_tokens as max_tokens if provided
            params["max_tokens"] = max_completion_tokens
            logging.info(f"Using max_completion_tokens={max_completion_tokens} as max_tokens for MiniMax model")

        # Add any additional parameters
        params.update(kwargs)

        chunk_count = 0
        total_tokens = 0

        stream = await self.client.chat.completions.create(**params)

        async for chunk in stream:
            if chunk.choices and chunk.choices[0].delta.content:
                chunk_content = chunk.choices[0].delta.content
                chunk_count += 1
                total_tokens += len(chunk_content)
                yield chunk_content

        logging.info(f"MiniMax streaming complete: {chunk_count} chunks, {total_tokens} chars total")

    async def embed(self, text: str, model: str = "minimax-embedding") -> List[float]:
        """
        Generate embeddings using Minimax's API.

        Args:
            text: The text to embed
            model: Minimax embedding model to use

        Returns:
            List of floating point values representing the embedding
        """
        # Note: This is a placeholder implementation as Minimax embedding
        # endpoint details weren't provided in the original code
        # You would need to replace this with the actual embedding API call

        if self.use_openai_client:
            response = await self.client.embeddings.create(
                model=model,
                input=text
            )
            return response.data[0].embedding
        else:
            # Example of direct REST API call for embeddings
            url = "https://api.minimaxi.chat/v1/embeddings"
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            payload = {
                "model": model,
                "input": text
            }

            response = requests.post(url, headers=headers, json=payload)
            response.raise_for_status()

            response_json = response.json()
            return response_json["data"][0]["embedding"]
#!/usr/bin/env python
"""
Simple test script that runs the same prompt through all available LLM providers.
"""
import os
import sys
import asyncio
import logging
from dotenv import load_dotenv

# Add parent directory to path to support absolute imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from gaia.llm.llm import LLM

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Load environment variables from .env file if present
load_dotenv()

# Test prompt
DEFAULT_PROMPT = "Generate a 100-word joke about chickens in space."

# Configure providers and models to test
PROVIDERS = [
    {
        "name": "openai",
        "model": "gpt-4o",
        "description": "OpenAI GPT-4o"
    },
    {
        "name": "openai", 
        "model": "o3-mini",
        "description": "OpenAI o3-mini (thinking model)"
    },
    {
        "name": "deepseek",
        "model": "deepseek-chat",
        "description": "Deepseek Chat"
    },
    {
        "name": "deepseek", 
        "model": "deepseek-reasoner",
        "description": "Deepseek Reasoner (thinking model)"
    },
    {
        "name": "minimax", 
        "model": "MiniMax-Text-01",
        "description": "MiniMax Text"
    }
]

async def test_provider(provider_config, prompt, streaming=False):
    """Test a specific LLM provider configuration."""
    provider_name = provider_config["name"]
    model_name = provider_config["model"]
    description = provider_config["description"]
    
    print(f"\n{'=' * 60}")
    print(f"Testing {description} ({provider_name}/{model_name})")
    print(f"{'=' * 60}")
    print(f"Prompt: {prompt}")
    print(f"Streaming: {streaming}")
    
    try:
        # Initialize LLM with the specified provider
        llm = LLM(provider=provider_name)
        
        # Prepare messages
        messages = [
            {"role": "system", "content": "You are a helpful AI assistant."},
            {"role": "user", "content": prompt}
        ]
        
        # Generate response
        if streaming:
            print("\nResponse:")
            full_response = ""
            async for chunk in llm.stream_generate(
                messages=messages,
                model=model_name,
                max_tokens=250
            ):
                print(chunk, end="", flush=True)
                full_response += chunk
            print("\n")
        else:
            response = await llm.generate(
                messages=messages,
                model=model_name,
                max_tokens=250
            )
            print("\nResponse:")
            print(response)
            print()
        
        print(f"{'=' * 60}")
        print(f"Completed test for {description}")
        print(f"{'=' * 60}\n")
        return True
        
    except Exception as e:
        print(f"\nError testing {description}: {str(e)}")
        print(f"{'=' * 60}\n")
        return False

async def main():
    prompt = DEFAULT_PROMPT
    streaming = True  # Set to False to disable streaming
    
    print("\n==== LLM Provider Testing Tool ====")
    print(f"Testing all providers with prompt: {prompt}")
    print(f"Streaming mode: {streaming}")
    
    results = []
    for provider_config in PROVIDERS:
        result = await test_provider(
            provider_config=provider_config,
            prompt=prompt,
            streaming=streaming
        )
        results.append((provider_config["description"], result))
    
    # Print summary
    print("\n==== Test Results Summary ====")
    for description, success in results:
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"{description}: {status}")
    
    # Count successful tests
    success_count = sum(1 for _, success in results if success)
    print(f"\n{success_count} of {len(PROVIDERS)} tests completed successfully.")

if __name__ == "__main__":
    asyncio.run(main()) 
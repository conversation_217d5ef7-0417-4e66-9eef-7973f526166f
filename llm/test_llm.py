#!/usr/bin/env python
"""
Basic test utility for the LLM abstraction layer.
Tests different LLM providers with a simple prompt.
"""
import os
import sys
import asyncio
import argparse
import logging
from typing import Optional
from dotenv import load_dotenv

# Add parent directory to path to support absolute imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from gaia.llm.llm import LLM

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Load environment variables from .env file if present
load_dotenv()

# Test prompt
DEFAULT_PROMPT = "Generate a 100-word joke about chickens in space."

async def test_generation(
    provider: str, 
    model: str, 
    prompt: str,
    streaming: bool = False, 
    max_tokens: Optional[int] = 250,
):
    """Test LLM generation with the specified provider and model."""
    logging.info(f"Testing {provider} provider with model {model}")
    logging.info(f"Prompt: {prompt}")
    logging.info(f"Streaming: {streaming}")
    
    # Initialize LLM with the specified provider
    llm = LLM(provider=provider)
    
    # Prepare messages
    messages = [
        {"role": "system", "content": "You are a helpful AI assistant."},
        {"role": "user", "content": prompt}
    ]
    
    # Generate response
    if streaming:
        logging.info("Streaming response:")
        print("\n--- Streaming Response ---")
        full_response = ""
        async for chunk in llm.stream_generate(
            messages=messages,
            model=model,
            max_tokens=max_tokens
        ):
            print(chunk, end="", flush=True)
            full_response += chunk
        print("\n------------------------")
        return full_response
    else:
        logging.info("Generating complete response:")
        response = await llm.generate(
            messages=messages,
            model=model,
            # max_tokens=max_tokens
        )
        print("\n--- Complete Response ---")
        print(response)
        print("------------------------")
        return response

async def main():
    # Test the specified provider
    # from gaia.prompts.decomposer_prompts import get_web_prompt, get_scholar_prompt, get_video_prompt, get_image_prompt
    # prompt = get_web_prompt(3, 15)
    # full_prompt = f"{prompt}\n\nUser Question: \"{'who is pete bernard?'}\""
    from gaia.prompts.decomposer_prompts import get_decomposition_prompt

    full_prompt = get_decomposition_prompt(
        "who is pete bernard?",
        3,
        15,
        context_type="web"
    )



    print(full_prompt)
    await test_generation(
        provider='openai',
        model='o3-mini',
        prompt=full_prompt,
        streaming=False,
        # max_tokens=8000
    )

if __name__ == "__main__":
    asyncio.run(main()) 
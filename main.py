import asyncio
import json
import re
import sys
from typing import List, Dict

from dotenv import load_dotenv
# Ensure API keys in .env are available before provider initialization
load_dotenv()

from llm import LLM
from planner import QueryDecomposer
from constraint import Constraint
from verifier import ToolSelector as PlanVerifier  # Re-use naming inside verifier.py
from tool_selector import ToolSelector
from prompts.tool_selector_prompt import tools as AVAILABLE_TOOLS
from dag_visualizer import DAGVisualizer
from pprint import pprint
# --------------------------- Verbose helpers --------------------------- #
SEP = "\n" + "=" * 120 + "\n"

# --------------------------- Utility helpers --------------------------- #

def safe_extract_json(text: str) -> Dict:
    """Extract the first JSON object found in a string and return it as dict."""
    cleaned = text.strip()
    if cleaned.startswith("```"):
        cleaned = re.sub(r"^```[a-zA-Z0-9]*\n", "", cleaned)
        cleaned = cleaned.rstrip("`").rstrip()
    match = re.search(r"\{[\s\S]*?\}", cleaned)
    if match:
        try:
            return json.loads(match.group(0))
        except json.JSONDecodeError as e:
            print(f"Warning: Failed to parse JSON: {e}")
            return {}
    return {}


async def run_pipeline(problem: str, threshold: int = 0, max_iters: int = 6):
    """Execute the iterative planning–verification pipeline."""
    llm = LLM(provider='openrouter')

    # Instantiate components
    planner = QueryDecomposer(llm)
    constraint_gen = Constraint(llm)
    verifier = PlanVerifier(llm)
    selector = ToolSelector(llm)

    # Initial tool set is the full list; selector will refine it
    current_tool_set: List[Dict] = AVAILABLE_TOOLS

    final_plan = None
    final_tool_ids: List[int] = []


    # 0. Generate the tool set

    tools = await selector.select_tools(problem, model="openai/gpt-oss-120b")
    tools = json.loads(tools)["tool_IDs"]


    actual_tools = {k: AVAILABLE_TOOLS[str(k)] for k in tools if str(k) in AVAILABLE_TOOLS}

    # 1. Generate plan with current tools
    plan = await planner.decompose_query(problem, tools, model='openai/gpt-oss-120b')
    print(f"{SEP}GENERATED PLAN{SEP}\n{plan}\n")

    # 2. Generate constraints
    constraints = await constraint_gen.get_constraints(problem, model='openai/gpt-oss-120b')
    print(f"{SEP}GENERATED CONSTRAINTS{SEP}\n{constraints}\n")


    # 3. Verify plan (with potential user clarification)
    verification_resp = await verifier.verify_plan(problem, plan, constraints, model='openai/gpt-oss-120b')
    print(f"{SEP}VERIFICATION RESPONSE{SEP}\n{verification_resp}\n")

    verification_json = safe_extract_json(str(verification_resp))

    score = verification_json.get("score", -100)
    plan_feedback = verification_json.get("plan_feedback", "")
    tools_feedback = verification_json.get("tools_feedback", "")
    print(f"Verifier score: {score}")

    # 4. Decision
    if isinstance(score, (int, float)) and score > threshold:
        final_plan = plan
        print()
        print()
        print()
        print()
        print()
        print()
        print()
        print()
        print()
        print("FinalPLAN")
        print("FinalPLAN")
        print("FinalPLAN")
        pprint(final_plan)
        
        # Generate DAG visualization of the final plan
        print(f"{SEP}GENERATING DAG VISUALIZATION{SEP}")
        visualizer = DAGVisualizer()
        visualizer.parse_tasks(final_plan)
        visualizer.print_analysis()
        
        # Create visualization with verifier feedback
        verifier_feedback = {
            'score': score,
            'plan_feedback': plan_feedback
        }
        visualizer.plot_dag(
            save_path='final_plan_dag.png',
            verifier_feedback=verifier_feedback,
            user_input=problem
        )
        print("DAG visualization saved to: final_plan_dag.png")

    else:

        for iteration in range(1, max_iters + 1):
            print(f"{SEP}ITERATION {iteration}{SEP}")

            # 0. Generate the tool set

            tools = await selector.select_tools(problem, model="openai/gpt-oss-120b", feedback=tools_feedback, selected_tools=actual_tools)
            tools = json.loads(tools)["tool_IDs"]
            actual_tools = {k: AVAILABLE_TOOLS[str(k)] for k in tools if str(k) in AVAILABLE_TOOLS}

            # 1. Generate plan with current tools
            plan = await planner.decompose_query(problem, tools, feedback=plan_feedback, plan=plan, model='openai/gpt-oss-120b')
            print(f"{SEP}GENERATED PLAN{SEP}\n{plan}\n")

            # 2. Generate constraints
            constraints = await constraint_gen.get_constraints(problem, model='openai/gpt-oss-120b')
            print(f"{SEP}GENERATED CONSTRAINTS{SEP}\n{constraints}\n")



            # 3. Verify plan (with potential user clarification)
            verification_resp = await verifier.verify_plan(problem, plan, constraints, model='openai/gpt-oss-120b')
            print(f"{SEP}VERIFICATION RESPONSE{SEP}\n{verification_resp}\n")

            verification_json = safe_extract_json(str(verification_resp))

            score = verification_json.get("score", -100)
            plan_feedback = verification_json.get("plan_feedback", "")
            tools_feedback = verification_json.get("tools_feedback", "")
            print(f"Verifier score: {score}")

            # 4. Decision
            if isinstance(score, (int, float)) and score > threshold:
                final_plan = plan
                print()
                print()
                print()
                print()
                print()
                print()
                print()
                print()
                print()
                print(f"FinalPLAN ITER {iteration}")
                print(f"FinalPLAN ITER {iteration}")
                print(f"FinalPLAN ITER {iteration}")
                pprint(final_plan)
                
                # Generate DAG visualization of the final plan
                print(f"{SEP}GENERATING DAG VISUALIZATION{SEP}")
                visualizer = DAGVisualizer()
                visualizer.parse_tasks(final_plan)
                visualizer.print_analysis()
                
                # Create visualization with verifier feedback
                verifier_feedback = {
                    'score': score,
                    'plan_feedback': plan_feedback
                }
                visualizer.plot_dag(
                    save_path='final_plan_dag.png',
                    verifier_feedback=verifier_feedback,
                    user_input=problem
                )
                print("DAG visualization saved to: final_plan_dag.png")
                print(f"Error generating DAG visualization: {e}")
                break




if __name__ == "__main__":

    problem_statement = """
plan my trip to thiland, find the top 3 cities and find me the best hotel in each city also find the best activity nearby the selected hotel for each city, plan for oct 1 to oct 30 2025 for 1 adult please use USD
    """
    asyncio.get_event_loop().run_until_complete(run_pipeline(problem_statement))
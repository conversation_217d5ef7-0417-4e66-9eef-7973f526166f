import asyncio
import json
import re
import logging
import os
import time
from pprint import pformat
from prompts.decomposition_prompts import get_decomposition_prompt
# import json_repair # Not used, can be removed if not planned for future use
import traceback
from llm import LLM

class QueryDecomposer:
    # Modified to accept LLM instance
    def __init__(self, llm: LLM, max_depth=3, max_total_nodes=50):
        # Ensure llm is passed
        if llm is None:
             raise ValueError("LLM instance is required.")
        self.llm = llm

    async def decompose_query(self, main_query, tools, feedback=None, plan=None, user_context="", model='openai/gpt-oss-120b'):
        """Decompose a complex query using the LLM and generate a primitives-only HTN plan."""
        try:


            prompt = get_decomposition_prompt(main_query, user_context, feedback=feedback, plan=plan, tools=tools)
            print(prompt)

            # Generate content using the configured decomposition model
            logging.info("DECOMPOSE | Sending planner prompt to LLM...")

            # Prepare messages for the LLM
            messages = [
                {"role": "assistant", "content": prompt},
                {"role": "user", "content": ""}
            ]

            # Get model from config
            

            # Generate content - for o3, only pass model and messages
            # No additional parameters for o3 as requested
            raw_content = await self.llm.generate(
                messages=messages,
                model=model
            )

            if not raw_content:
                logging.error("DECOMPOSE | Empty or failed response from LLM. Returning query as question.")
                return main_query # Or a more specific question object/string

            logging.info(f"DECOMPOSE | Raw response content received (first 500 chars): {raw_content[:500]}...")

            plan_json = None
            question_text = None

            try:
                plan_json = json.loads(raw_content)
                logging.info("DECOMPOSE | Successfully parsed raw response as JSON.")
            except json.JSONDecodeError:
                logging.info("DECOMPOSE | Failed to parse raw response as JSON. Assuming it's a question.")
                question_text = raw_content.strip()
                return question_text

            # Check if the parsed JSON is a dictionary and contains the 'tasks' key as expected for our primitives-only plan
            if isinstance(plan_json, dict) and 'tasks' in plan_json and isinstance(plan_json['tasks'], list):
                logging.info("DECOMPOSE | Detected HTN-like plan structure with 'tasks' key.")

                primitive_tasks = []
                for task in plan_json['tasks']:
                    if isinstance(task, dict) and task.get('type') == 'primitive':
                        # Basic validation for essential fields in a primitive task
                        if not task.get('id') or not task.get('orchestration_action') or not task.get('name'):
                            logging.warning(f"DECOMPOSE | Primitive task missing essential fields (id, orchestration_action, name). Skipping task: {pformat(task)}")
                            continue
                        primitive_tasks.append(task)
                    elif isinstance(task, dict):
                        logging.warning(f"DECOMPOSE | Received a non-primitive task or task with missing/invalid 'type' in plan: '{task.get('id', 'Unknown ID')}' (type: {task.get('type', 'N/A')}). It will be ignored.")

                if not primitive_tasks:
                    if plan_json['tasks']: # Tasks list was present and non-empty, but all were filtered
                        logging.error("DECOMPOSE | Plan contained tasks, but no valid primitive tasks found after filtering. Returning raw content as question.")
                    else: # Tasks list was empty
                        logging.warning("DECOMPOSE | Plan contained an empty 'tasks' list. Returning raw content as question.")
                    return raw_content.strip() # Return original raw content as question

                final_plan = {"tasks": primitive_tasks}
                logging.info(f"DECOMPOSE | Successfully processed into primitives-only plan with {len(primitive_tasks)} task(s).") # Removed pformat for brevity
                # logging.debug(f"DECOMPOSE | Final plan: {pformat(final_plan)}") # For more detailed logging if needed
                return final_plan

            else:
                # Parsed JSON was not in the expected format (e.g., not a dict, or no 'tasks' list)
                logging.warning("DECOMPOSE | Parsed JSON, but not in the expected primitives-only HTN structure (e.g., missing 'tasks' list or not a dict). Assuming question.")
                question_text = raw_content.strip()
                return question_text

        except Exception as e:
            logging.error(f"DECOMPOSE | Unexpected error during query decomposition: {e}")
            logging.error(traceback.format_exc())
            # Fallback to returning the original main_query as a question in case of unexpected errors
            return main_query # Or a more specific error/question representation

    # _is_valid_plan and _create_fallback_plan were for a different plan structure (Orchestrate DAG).
    # They are not directly compatible with the new primitives-only HTN structure.
    # If a fallback is needed for this new structure, it would have to be redesigned.
    # For now, errors lead to returning a question or the original query.

    def _is_valid_plan(self, plan):
        """This method is for the old 'Orchestrate' DAG structure and likely not used with the new HTN primitives-only output."""
        logging.warning("DECOMPOSE | _is_valid_plan called, but it's designed for 'Orchestrate' DAGs, not the current HTN primitives-only plan.")
        # Retain original logic for now in case it's called unexpectedly, but it's not relevant for the new flow.
        if not isinstance(plan, dict) or 'nodes' not in plan or 'edges' not in plan:
            logging.warning(f"Invalid plan structure: Missing 'nodes' or 'edges'. Plan: {pformat(plan)}")
            return False

        if not isinstance(plan['nodes'], list) or not isinstance(plan['edges'], list):
            logging.warning(f"Invalid plan structure: 'nodes' or 'edges' is not a list. Plan: {pformat(plan)}")
            return False

        # Define the required fields for each node based on "Orchestrate"
        required_node_fields = {"id", "action", "parameters", "input_refs", "output_ref"}
        # Define the allowed actions

        node_ids = set()
        for node in plan['nodes']:
            if not isinstance(node, dict):
                logging.warning(f"Invalid node type: Node is not a dictionary. Node: {pformat(node)}")
                return False

            # Check for required fields
            if not required_node_fields.issubset(node.keys()):
                missing_fields = required_node_fields - node.keys()
                logging.warning(f"Invalid node: Missing required fields {missing_fields}. Node: {pformat(node)}")
                return False

            # Check field types (basic checks)
            if not isinstance(node['id'], str) or not node['id']:
                logging.warning(f"Invalid node: 'id' must be a non-empty string. Node: {pformat(node)}")
                return False
            if not isinstance(node['parameters'], dict):
                logging.warning(f"Invalid node: 'parameters' must be a dictionary. Node: {pformat(node)}")
                return False
            if not isinstance(node['input_refs'], list):
                 logging.warning(f"Invalid node: 'input_refs' must be a list. Node: {pformat(node)}")
                 return False
            if not isinstance(node['output_ref'], str) or not node['output_ref']:
                 logging.warning(f"Invalid node: 'output_ref' must be a non-empty string. Node: {pformat(node)}")
                 return False
            if 'description' in node and not isinstance(node['description'], str):
                 logging.warning(f"Invalid node: 'description' must be a string if present. Node: {pformat(node)}")
                 return False

            if node['id'] in node_ids:
                logging.warning(f"Invalid plan: Duplicate node id '{node['id']}' found.")
                return False
            node_ids.add(node['id'])

        # Check edges
        for edge in plan['edges']:
             if not isinstance(edge, dict) or 'source' not in edge or 'target' not in edge:
                 logging.warning(f"Invalid edge format: Missing 'source' or 'target'. Edge: {pformat(edge)}")
                 return False
             if not isinstance(edge['source'], str) or not isinstance(edge['target'], str):
                  logging.warning(f"Invalid edge format: 'source' and 'target' must be strings. Edge: {pformat(edge)}")
                  return False
             if edge['source'] not in node_ids or edge['target'] not in node_ids:
                  logging.warning(f"Invalid edge: Source '{edge['source']}' or target '{edge['target']}' not found in node ids. Edge: {pformat(edge)}")
                  return False

        # Optional: Add more sophisticated DAG validation (e.g., check for cycles) if needed
        return True

    def _create_fallback_plan(self, main_query):
        """This method creates an 'Orchestrate'-style fallback and is not directly compatible with the new HTN primitives-only output."""
        logging.warning("DECOMPOSE | _create_fallback_plan called, but it creates an 'Orchestrate'-style plan, not HTN primitives-only.")
        # Retain original logic for now in case it's called unexpectedly.
        fallback_node_id = "fallback_analyze_query"
        fallback_plan = {
            "nodes": [
                {
                    "id": fallback_node_id,
                    "action": "ANALYZE",
                    "parameters": {"query_text": main_query},
                    "input_refs": [],
                    "output_ref": "analysis_result",
                    "description": f"Fallback task: Analyze the main query: '{main_query}'"
                }
            ],
            "edges": []
        }
        logging.info("DECOMPOSE | Created 'Orchestrate' fallback plan (note: this may not be compatible with current HTN processing path).")
        return fallback_plan
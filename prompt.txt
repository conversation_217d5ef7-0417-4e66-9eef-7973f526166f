prompt.py:


from prompts.tool_selector_prompt import tools as tools_desc




def get_decomposition_prompt(main_query, user_context="", feedback=None, plan=None, tools=[]):
    """
    Constructs a concise HTN decomposition prompt for o3-mini.
    """
    context_section = ""
    if user_context:
        context_section = "\n\n# PREVIOUS CONTEXT / USER ANSWERS\n" + user_context + "\n"

    # Create the prompt with the main query and context section
    tools_desc_str = ""
    tool_names_str = ""
    for tool in tools:
        tool = f"{tool}"
        tool_names_str += f"""    "{tools_desc[tool]['tool_name']}",\n"""


        tools_desc_str += f"""  - "{tools_desc[tool]['tool_name']}": {tools_desc[tool]['tool_description']}\n"""
        tools_desc_str += "    Available endpoints:\n"
        for endpoint in tools_desc[tool]['endpoint']:
            tools_desc_str += f"    - `{endpoint['type']} {endpoint['endpoint_description']}`\n"
        tools_desc_str += "\n"

    feedback_prompt = """
    # PREVIOUS ITERATION
    From the previous iteration we obtained the following plan and feedback:

    ## plan:
    {plan}

    ## feedback:
    {feedback}

    - to create the new plan consider the given feedback
    """

    if feedback and plan:
      feedback_prompt = feedback_prompt.replace("{feedback}", feedback).replace("{plan}", plan)
    else:
      feedback_prompt = ""
    prompt = """
# ROLE
You are an HTN planner. Decompose the user request into a plan using the provided actions.

# CORE HTN CONCEPTS TO APPLY
-- Tasks: Units of work (Compound or Primitive).
-- Methods: Define how to achieve compound tasks using subtasks (primitive or compound).
-- Ordering: Use 'ordered' (with integer 'order' values for sequence/parallelism) or 'unordered'.
-- Primitive Tasks: Use exactly one 'orchestration_action' from the list below.

+HTN Planning involves breaking down high-level goals into smaller, manageable steps using a predefined set of tasks and methods:
+- **Tasks:** Represent goals or actions.
+  - **Compound Tasks:** Abstract goals that cannot be executed directly (e.g., "Plan Trip"). They require *Methods* to specify how they can be achieved.
+  - **Primitive Tasks:** Concrete, directly executable actions that correspond to a single `orchestration_action` (e.g., "Search for Flights"). These are the leaves of the plan hierarchy.
+- **Methods:** Define one specific way (a recipe or strategy) to decompose a *Compound Task* into a sequence or set of subtasks (which can be compound or primitive).
+  - Each Method has **Preconditions:** Conditions that must be true before the method can be applied.
+  - Each Method defines **Subtasks:** The steps needed to achieve the compound task according to this method.
+  - Each Method specifies **Ordering:**
+    - `ordered`: Subtasks must be executed respecting the integer `order` values. Tasks with the same `order` can run in parallel. Tasks with a lower `order` must complete before tasks with a higher `order` begin.
+    - `unordered`: Subtasks can execute in any order or in parallel, subject only to their individual preconditions.
+- **Goal:** Decompose the initial high-level task (derived from the User Query) recursively using applicable Methods until only Primitive Tasks remain.
+
+# TASK PRECONDITION PRINCIPLES
+*Task Preconditions*: When constructing the plan, ensure that Preconditions between tasks are explicitly and accurately represented. Each task should only be linked to those tasks on which it directly depends, based on the logical structure of the decomposition.
+  - Inherit Preconditions from the hierarchical breakdown: a task's Preconditions should reflect only the minimal and necessary set of preceding tasks required for its execution, as determined by the plan's structure.
+  - Avoid redundant or irrelevant Preconditions: do not link a task to any other task unless there is a direct and meaningful Preconditions.
+  - The Preconditions structure should be both complete (all true Preconditions are represented) and minimal (no unnecessary or spurious Preconditions are included).
+  - Use the `preconditions` field in tasks and methods to specify these Preconditions, referencing the outputs or completion of other tasks as needed (e.g., "results_of_task_A completed").
+  - This ensures clarity, correctness, and efficiency in the resulting plan, and supports accurate parallelization and sequencing of tasks.

# ORCHESTRATION ACTION SET
Available orchestration actions: [
"""+tool_names_str+"""    "generate_final_answers",
    "generate_structured_data",
    "rank_data",
    "evaluate_data",
    "select_data"
].
Every *primitive* task must be assigned one "orchestration_action".
"""+tools_desc_str+"""

  - "generate_final_answers": Generate final answers based on processed data and the original user question.
    Inputs:
      - `processed_data` (any): The processed data to generate answers from.
      - `original_question` (Optional[str]): The original question asked by the user (optional).
      - `format` (str): Output format ("markdown", "json", "text"). Default: "markdown".
    Output:
      Dict with keys:
        - `answer` (str): The generated answer in the specified format.
        - `sections` (List[Dict[str, str]]): Extracted sections with "title" and "content".
        - `summary` (str): A brief summary of the answer.
        - `recommendations` (List[str]): Key recommendations or actionable insights.
  - "generate_structured_data": Generate a structured data file (e.g., json, csv, markdown) from input data.
    Inputs:
      - `input_data` (any): The data to be structured.
      - `output_type` (str): Type of output file ("json", "csv", "markdown").
      - `output_filename` (str): Name of the output file (without extension).
      - `num_pages` (Optional[int]): Number of pages or sections for paginated output (optional).
    Output:
      Dict with keys:
        - `file_path` (str): Path to the generated file.
        - `content_preview` (str): Preview of the file content (first few lines).
        - `structure_description` (str): Brief description of the structure and any key insights.
        - `file_stats` (Dict): File statistics, including `size_bytes` (int) and `line_count` (int).
  - "rank_data": Rank data based on specific criteria.
    Inputs:
      - `prompt` (str): **Crucial.** A detailed, self-contained instruction generated by the planner. This prompt tells the `rank_data` tool *what specific items to identify and extract* from the `input_data` for ranking, and *how to approach the ranking task conceptually*. For example: "From the provided web search snippets [input_data], identify all unique city names in Thailand. Then, rank these identified cities based on their suitability for tourists to determine the top 5."
      - `input_data` (List or Dict): The data source from which items will be identified (based on the `prompt`) and then ranked.
      - `criteria` (str or Dict): Specific criteria to rank the *identified items*. This complements the `prompt`. E.g., {"tourist_attractions": "high", "safety": "high"}.
    Output:
      Dict with keys: `items_identified_for_ranking` (List), `ranked_items` (List of {{ "rank": int, "item": "<item_object_or_string>", "score": "<numerical_score_0_to_100>", "explanation": str }}), `overall_ranking_summary` (str), `top_item_details` (Optional[Dict]).
  - "evaluate_data": Evaluate data, compare options, and make judgments. Inputs: `input_data` (any), `criteria` (str or Dict). Output: Dict with keys: `summary` (str), `detailed_analysis` (Dict), `strengths` (List[str]), `weaknesses` (List[str]), `recommendations` (List[str]), `overall_assessment` (Dict with keys: `score` (int), `rating` (str), `conclusion` (str)).
  - "select_data": Choose options based on evaluation or criteria.
    Inputs:
      - `prompt` (str): **Crucial.** A detailed, self-contained instruction generated by the planner. This prompt tells the `select_data` tool *what specific items to consider for selection* from the `options` data, and *how to approach the selection task conceptually*. For example: "From the provided list of hotels [options], select the best hotel that offers a gym and has a rating above 4.5 stars."
      - `options` (List or Dict): The pool of items from which to select.
      - `criteria` (str or Dict): Specific criteria to select the items. This complements the `prompt`. E.g., {"amenities": "gym", "min_rating": 4.5}.
      - `topk` (int, default=1): Number of items to select.
      - `explain` (bool, default=True): Whether to include explanations.
    Output:
      Dict with keys: `selected_options` (List of {{ "option": option_object, "explanation": str, "confidence": float }}), `alternatives_considered` (List of {{ "option": option_object, "reason_not_selected": str }}), and `criteria_applied` (str).

# USER QUERY
""" + main_query + """
""" + context_section + """
# STEP-BY-STEP INSTRUCTIONS

## Step 1: Hierarchical Decomposition
- Begin by breaking down the user's high-level goal into a hierarchy:
  - Identify the main compound task(s) that represent the overall objective.
  - For each compound task, define one or more methods that describe alternative ways to achieve the task.
  - Each method should further decompose into subtasks, which may themselves be compound or primitive.

## Step 2: Logical and Parallel Structure
- For each method, analyze the logical dependencies among subtasks:
  - Assign the same `order` value to subtasks that are logically independent and can be executed in parallel.
  - Use increasing `order` values for subtasks that must be executed sequentially.
  - Choose `ordered` or `unordered` for the method's `ordering` field as appropriate.

## Step 3: Full Decomposition to Primitive Tasks
- Continue decomposing all compound tasks until every leaf node is a primitive task.
- Primitive tasks must correspond directly to a single orchestration action.

## Step 4: Assign Orchestration Actions and Parameters
- For each primitive task:
  - Assign exactly one `orchestration_action` from the ORCHESTRATION ACTION SET, using the exact action name.
  - Populate the `parameters` field using only the exact parameter names specified for that action.
  - When a parameter value should come from a previous task's output, use one of these reference formats:
    - `results_of_TASK_ID` to reference the entire result of a task
    - `output_of_TASK_ID` as an alternative to reference the entire result
    - `FIELD_from_TASK_ID` to reference a specific field from a task's result
    - For nested data or to access specific keys from a task's output dictionary (this dictionary is typically found under a `data` key in the full task result object referenced by `results_of_TASK_ID`): use `results_of_TASK_ID.data.key_name`. Example: `results_of_geocode_task.data.latitude`.
    - If `key_name` refers to an array (a list of items) and you need to access a specific element from that array by its index: use `results_of_TASK_ID.data.array_key_name[index]`. Example: `results_of_search_task.data.hotel_list[0]`.
    - To access a sub-property of an object that is an element within such an array: use `results_of_TASK_ID.data.array_key_name[index].sub_property`. For instance, to correctly access the `item` sub-property from the first element of a `ranked_items` array (where `ranked_items` is a key in the output dictionary of a task like `t2_rank_cities`), use: `results_of_t2_rank_cities.data.ranked_items[0].item`. Note the consistent use of the `results_of_TASK_ID.data.` prefix before accessing keys like `ranked_items` from the tool's direct output dictionary.
  - Do NOT use template variables like `{{variable_name}}` in parameters.
  - If any required parameter is missing, do not guess—ask the user a specific question about the missing information (see Output Format B).
  - **Generating Prompts for Tools**: For actions that include a `prompt` parameter (e.g., `rank_data`, `select_data`), this `prompt` value is NOT typically derived from a previous task's direct output field. Instead, **YOU (the HTN Planner) must generate a clear, detailed, and self-contained instruction string for this `prompt` parameter.** This generated prompt should guide the tool on what specific sub-task to perform using its other inputs (like `input_data` or `options`) in the context of the overall user query and the current step in your plan.
    - *Example for `rank_data`*: If the goal is to rank cities from web search results (e.g., `results_of_t1_search_cities`), the `prompt` parameter for `rank_data` should be a string you generate, such as: `"From the provided web search snippets in 'results_of_t1_search_cities', identify all unique city names mentioned. Then, rank these identified cities based on tourist popularity, available attractions, and user reviews to find the top 5 cities for a trip to Thailand."`
    - *Example for `select_data`*: If selecting a hotel from a list (e.g., `results_of_t2_search_hotels`), the `prompt` parameter for `select_data` could be: `"From the list of hotels in 'results_of_t2_search_hotels', select the one hotel that best balances a high user rating (above 4.2 stars), proximity to the city center, and includes breakfast, for a 1-adult stay."`
    - The `criteria` parameter for these tools should then contain the specific, structured criteria that complement this generated prompt (e.g., `{"min_rating": 4.2, "amenities": ["breakfast"], "location_preference": "city_center"}`).

## Step 5: Define Explicit Preconditions
- For each task and method, specify the minimal and necessary `preconditions`:
  - Only reference tasks that are direct and logical prerequisites, as determined by the plan's structure.
  - Use clear references to outputs or completion of other tasks (e.g., "results_of_task_A completed").
  - Avoid redundant or irrelevant preconditions.

## Examples of Proper Parameter Referencing
- CORRECT: Using a task's output as a parameter
  ```json
  {
    "id": "t2_search_hotels",
    "type": "primitive",
    "name": "Search for hotels in the selected city",
    "orchestration_action": "search_hotel",
    "parameters": {
      "destination": "results_of_t1_select_city.data.selected_city",
      "check_in": "2025-10-01",
      "check_out": "2025-10-05"
    },
    "preconditions": ["t1_select_city completed"]
  }
  ```

- INCORRECT: Using template variables
  ```json
  {
    "id": "t2_search_hotels",
    "type": "primitive",
    "name": "Search for hotels in the selected city",
    "orchestration_action": "search_hotel",
    "parameters": {
      "destination": "{{selected_city}}",  // WRONG FORMAT
      "check_in": "2025-10-01",
      "check_out": "2025-10-05"
    },
    "preconditions": ["t1_select_city completed"]
  }
  ```

## Step 6: Ensure Plan Integrity
- Review the entire plan to confirm:
  - The structure is a valid, complete HTN (Hierarchical Task Network).
  - All tasks are fully decomposed to primitive tasks.
  - There are no gaps, redundancies, or logical inconsistencies.
  - The final JSON output plan *only* includes these primitive tasks. Compound tasks and methods, while used in your internal decomposition process, should not be part of the final JSON output's "tasks" or "methods" lists. The "tasks" list should contain only primitive tasks, and the "methods" list should be omitted. Preconditions for primitive tasks must refer to other primitive task IDs or initial states.

## Step 7: Verification and Correction
- Carefully check that every instruction above has been followed.
- If any part of the plan is incomplete, incorrect, or non-compliant, restart from Step 1 and repeat the process until the plan is fully valid.

## Step 8: Handling Missing Information
- If any information required for decomposition or for filling in parameters is missing:
  - Output a single, specific question to the user requesting only the minimal information needed to proceed (see Output Format B).
- If all required information is present, output the JSON plan (see Output Format A).

"""+feedback+"""


# IMPORTANT JSON OUTPUT RULES

**A. Do NOT generate any comments inside the JSON output.**
This includes both single-line and block comments, such as:
- // This is a comment
- /* This is a block comment */
- Any text like: "/* ---------- City 5 Activity Tasks ---------- */" or "/* ------------------------------------------------- */"

**B. The JSON output must be strictly valid and directly parsable using the Python `json` library.**
- Do not include any comments, trailing commas, or extra text outside the JSON object.
- Only output the JSON object itself, with correct syntax and structure.

# OUTPUT FORMAT
EITHER:
  A) If the user query provides sufficient information to create a complete and detailed HTN plan (including necessary parameters for all primitive actions), return ONLY a VALID JSON object with a "tasks" key, adhering strictly to the specified fields for primitive tasks. The "methods" key should be omitted.
  OR
  B) If the user query is ambiguous or lacks specific details needed to define tasks, methods, or parameters for orchestration actions, return ONLY plain text asking the user a specific question to clarify the missing information. Do NOT return any JSON in this case. Focus the question on the *minimal* information needed to proceed.

JSON Structure (If generating plan):
{
  "tasks": [ // This list MUST ONLY contain PRIMITIVE tasks
    {
      "id": "unique_primitive_task_id",
      "type": "primitive", // This MUST be "primitive"
      "name": "Human-readable task name",
      "orchestration_action": "ACTION_NAME", // This MUST be specified for primitive tasks
      "parameters": {}, // Parameters for the primitive action
      "preconditions": ["primitive_task_id_1 completed", ...] // Preconditions MUST refer to other PRIMITIVE task IDs or initial conditions
    }
    // ... more primitive tasks, no comments allowed
  ]
  // The "methods" array should NOT be included in the final output.
  // Compound tasks should NOT be included in the "tasks" array in the final output.
}

Ensure the JSON is valid and contains no extra text or explanations. Do not include any comments in the JSON output. The output must be directly parsable by the Python `json` library.

"""
    return prompt





userquery:
plan my trip to thiland, find the top 2 cities and find me the best hotel in each city also find the best activity nearby the selected hotel for each city, plan for oct 1 to oct 30 2025 for 1 adult please use USD




output:

{
  "tasks": [
    {
      "id": "t1_search_cities",
      "type": "primitive",
      "name": "Global search for best cities to visit in Thailand",
      "orchestration_action": "global_search",
      "parameters": {
        "query": "best cities to visit in Thailand for tourists 2025",
        "engines": [
          "google",
          "bing"
        ],
        "max_results": 50
      },
      "preconditions": []
    },
    {
      "id": "t2_rank_cities",
      "type": "primitive",
      "name": "Rank the top 5 Thai cities for the trip",
      "orchestration_action": "rank_data",
      "parameters": {
        "prompt": "From the provided web search snippets in results_of_t1_search_cities, identify all unique city names in Thailand that are recommended for tourists. Then rank these cities based on overall tourist popularity, diversity of attractions, safety, and accessibility to determine the top 5 cities for a month-long visit in October 2025.",
        "input_data": "results_of_t1_search_cities",
        "criteria": {
          "popularity": "high",
          "attractions_diversity": "high",
          "safety": "high",
          "accessibility": "high",
          "topk": 5
        }
      },
      "preconditions": [
        "t1_search_cities completed"
      ]
    },
    {
      "id": "t3_search_hotels_city1",
      "type": "primitive",
      "name": "Search hotels in top city #1",
      "orchestration_action": "search_hotel",
      "parameters": {
        "destination": "results_of_t2_rank_cities.data.ranked_items[0].item",
        "check_in": "2025-10-01",
        "check_out": "2025-10-30",
        "adults": 1,
        "children": 0,
        "currency": "USD"
      },
      "preconditions": [
        "t2_rank_cities completed"
      ]
    },
    {
      "id": "t4_select_hotel_city1",
      "type": "primitive",
      "name": "Select best hotel in city #1",
      "orchestration_action": "select_data",
      "parameters": {
        "prompt": "From the hotel search results in results_of_t3_search_hotels_city1, select the single best hotel for one adult staying from Oct 1 to Oct 30 2025, prioritizing rating (minimum 4), guest reviews, value for money, free Wi-Fi, and central location.",
        "options": "results_of_t3_search_hotels_city1",
        "criteria": {
          "min_rating": 4,
          "amenities": [
            "free WiFi"
          ],
          "location_preference": "central"
        },
        "topk": 1,
        "explain": true
      },
      "preconditions": [
        "t3_search_hotels_city1 completed"
      ]
    },
    {
      "id": "t5_search_activity_city1",
      "type": "primitive",
      "name": "Search activities near selected hotel in city #1",
      "orchestration_action": "search_activity",
      "parameters": {
        "query": "tourist attractions near results_of_t4_select_hotel_city1.data.selected_options[0].option.name",
        "radius": 5000,
        "place_type": "tourist_attraction"
      },
      "preconditions": [
        "t4_select_hotel_city1 completed"
      ]
    },
    {
      "id": "t6_select_activity_city1",
      "type": "primitive",
      "name": "Select best activity near hotel in city #1",
      "orchestration_action": "select_data",
      "parameters": {
        "prompt": "From the attractions listed in results_of_t5_search_activity_city1, select the single best activity that offers a memorable experience, is highly rated (4+), and is within 5 km of the selected hotel.",
        "options": "results_of_t5_search_activity_city1",
        "criteria": {
          "rating_min": 4,
          "distance_km_max": 5,
          "popularity": "high"
        },
        "topk": 1,
        "explain": true
      },
      "preconditions": [
        "t5_search_activity_city1 completed"
      ]
    },
    {
      "id": "t7_search_hotels_city2",
      "type": "primitive",
      "name": "Search hotels in top city #2",
      "orchestration_action": "search_hotel",
      "parameters": {
        "destination": "results_of_t2_rank_cities.data.ranked_items[1].item",
        "check_in": "2025-10-01",
        "check_out": "2025-10-30",
        "adults": 1,
        "children": 0,
        "currency": "USD"
      },
      "preconditions": [
        "t2_rank_cities completed"
      ]
    },
    {
      "id": "t8_select_hotel_city2",
      "type": "primitive",
      "name": "Select best hotel in city #2",
      "orchestration_action": "select_data",
      "parameters": {
        "prompt": "From the hotel search results in results_of_t7_search_hotels_city2, select the single best hotel for one adult staying from Oct 1 to Oct 30 2025, prioritizing rating (minimum 4), guest reviews, value for money, free Wi-Fi, and central location.",
        "options": "results_of_t7_search_hotels_city2",
        "criteria": {
          "min_rating": 4,
          "amenities": [
            "free WiFi"
          ],
          "location_preference": "central"
        },
        "topk": 1,
        "explain": true
      },
      "preconditions": [
        "t7_search_hotels_city2 completed"
      ]
    },
    {
      "id": "t9_search_activity_city2",
      "type": "primitive",
      "name": "Search activities near selected hotel in city #2",
      "orchestration_action": "search_activity",
      "parameters": {
        "query": "tourist attractions near results_of_t8_select_hotel_city2.data.selected_options[0].option.name",
        "radius": 5000,
        "place_type": "tourist_attraction"
      },
      "preconditions": [
        "t8_select_hotel_city2 completed"
      ]
    },
    {
      "id": "t10_select_activity_city2",
      "type": "primitive",
      "name": "Select best activity near hotel in city #2",
      "orchestration_action": "select_data",
      "parameters": {
        "prompt": "From the attractions listed in results_of_t9_search_activity_city2, select the single best activity that offers a memorable experience, is highly rated (4+), and is within 5 km of the selected hotel.",
        "options": "results_of_t9_search_activity_city2",
        "criteria": {
          "rating_min": 4,
          "distance_km_max": 5,
          "popularity": "high"
        },
        "topk": 1,
        "explain": true
      },
      "preconditions": [
        "t9_search_activity_city2 completed"
      ]
    },
    {
      "id": "t11_search_hotels_city3",
      "type": "primitive",
      "name": "Search hotels in top city #3",
      "orchestration_action": "search_hotel",
      "parameters": {
        "destination": "results_of_t2_rank_cities.data.ranked_items[2].item",
        "check_in": "2025-10-01",
        "check_out": "2025-10-30",
        "adults": 1,
        "children": 0,
        "currency": "USD"
      },
      "preconditions": [
        "t2_rank_cities completed"
      ]
    },
    {
      "id": "t12_select_hotel_city3",
      "type": "primitive",
      "name": "Select best hotel in city #3",
      "orchestration_action": "select_data",
      "parameters": {
        "prompt": "From the hotel search results in results_of_t11_search_hotels_city3, select the single best hotel for one adult staying from Oct 1 to Oct 30 2025, prioritizing rating (minimum 4), guest reviews, value for money, free Wi-Fi, and central location.",
        "options": "results_of_t11_search_hotels_city3",
        "criteria": {
          "min_rating": 4,
          "amenities": [
            "free WiFi"
          ],
          "location_preference": "central"
        },
        "topk": 1,
        "explain": true
      },
      "preconditions": [
        "t11_search_hotels_city3 completed"
      ]
    },
    {
      "id": "t13_search_activity_city3",
      "type": "primitive",
      "name": "Search activities near selected hotel in city #3",
      "orchestration_action": "search_activity",
      "parameters": {
        "query": "tourist attractions near results_of_t12_select_hotel_city3.data.selected_options[0].option.name",
        "radius": 5000,
        "place_type": "tourist_attraction"
      },
      "preconditions": [
        "t12_select_hotel_city3 completed"
      ]
    },
    {
      "id": "t14_select_activity_city3",
      "type": "primitive",
      "name": "Select best activity near hotel in city #3",
      "orchestration_action": "select_data",
      "parameters": {
        "prompt": "From the attractions listed in results_of_t13_search_activity_city3, select the single best activity that offers a memorable experience, is highly rated (4+), and is within 5 km of the selected hotel.",
        "options": "results_of_t13_search_activity_city3",
        "criteria": {
          "rating_min": 4,
          "distance_km_max": 5,
          "popularity": "high"
        },
        "topk": 1,
        "explain": true
      },
      "preconditions": [
        "t13_search_activity_city3 completed"
      ]
    },
    {
      "id": "t15_search_hotels_city4",
      "type": "primitive",
      "name": "Search hotels in top city #4",
      "orchestration_action": "search_hotel",
      "parameters": {
        "destination": "results_of_t2_rank_cities.data.ranked_items[3].item",
        "check_in": "2025-10-01",
        "check_out": "2025-10-30",
        "adults": 1,
        "children": 0,
        "currency": "USD"
      },
      "preconditions": [
        "t2_rank_cities completed"
      ]
    },
    {
      "id": "t16_select_hotel_city4",
      "type": "primitive",
      "name": "Select best hotel in city #4",
      "orchestration_action": "select_data",
      "parameters": {
        "prompt": "From the hotel search results in results_of_t15_search_hotels_city4, select the single best hotel for one adult staying from Oct 1 to Oct 30 2025, prioritizing rating (minimum 4), guest reviews, value for money, free Wi-Fi, and central location.",
        "options": "results_of_t15_search_hotels_city4",
        "criteria": {
          "min_rating": 4,
          "amenities": [
            "free WiFi"
          ],
          "location_preference": "central"
        },
        "topk": 1,
        "explain": true
      },
      "preconditions": [
        "t15_search_hotels_city4 completed"
      ]
    },
    {
      "id": "t17_search_activity_city4",
      "type": "primitive",
      "name": "Search activities near selected hotel in city #4",
      "orchestration_action": "search_activity",
      "parameters": {
        "query": "tourist attractions near results_of_t16_select_hotel_city4.data.selected_options[0].option.name",
        "radius": 5000,
        "place_type": "tourist_attraction"
      },
      "preconditions": [
        "t16_select_hotel_city4 completed"
      ]
    },
    {
      "id": "t18_select_activity_city4",
      "type": "primitive",
      "name": "Select best activity near hotel in city #4",
      "orchestration_action": "select_data",
      "parameters": {
        "prompt": "From the attractions listed in results_of_t17_search_activity_city4, select the single best activity that offers a memorable experience, is highly rated (4+), and is within 5 km of the selected hotel.",
        "options": "results_of_t17_search_activity_city4",
        "criteria": {
          "rating_min": 4,
          "distance_km_max": 5,
          "popularity": "high"
        },
        "topk": 1,
        "explain": true
      },
      "preconditions": [
        "t17_search_activity_city4 completed"
      ]
    },
    {
      "id": "t19_search_hotels_city5",
      "type": "primitive",
      "name": "Search hotels in top city #5",
      "orchestration_action": "search_hotel",
      "parameters": {
        "destination": "results_of_t2_rank_cities.data.ranked_items[4].item",
        "check_in": "2025-10-01",
        "check_out": "2025-10-30",
        "adults": 1,
        "children": 0,
        "currency": "USD"
      },
      "preconditions": [
        "t2_rank_cities completed"
      ]
    },
    {
      "id": "t20_select_hotel_city5",
      "type": "primitive",
      "name": "Select best hotel in city #5",
      "orchestration_action": "select_data",
      "parameters": {
        "prompt": "From the hotel search results in results_of_t19_search_hotels_city5, select the single best hotel for one adult staying from Oct 1 to Oct 30 2025, prioritizing rating (minimum 4), guest reviews, value for money, free Wi-Fi, and central location.",
        "options": "results_of_t19_search_hotels_city5",
        "criteria": {
          "min_rating": 4,
          "amenities": [
            "free WiFi"
          ],
          "location_preference": "central"
        },
        "topk": 1,
        "explain": true
      },
      "preconditions": [
        "t19_search_hotels_city5 completed"
      ]
    },
    {
      "id": "t21_search_activity_city5",
      "type": "primitive",
      "name": "Search activities near selected hotel in city #5",
      "orchestration_action": "search_activity",
      "parameters": {
        "query": "tourist attractions near results_of_t20_select_hotel_city5.data.selected_options[0].option.name",
        "radius": 5000,
        "place_type": "tourist_attraction"
      },
      "preconditions": [
        "t20_select_hotel_city5 completed"
      ]
    },
    {
      "id": "t22_select_activity_city5",
      "type": "primitive",
      "name": "Select best activity near hotel in city #5",
      "orchestration_action": "select_data",
      "parameters": {
        "prompt": "From the attractions listed in results_of_t21_search_activity_city5, select the single best activity that offers a memorable experience, is highly rated (4+), and is within 5 km of the selected hotel.",
        "options": "results_of_t21_search_activity_city5",
        "criteria": {
          "rating_min": 4,
          "distance_km_max": 5,
          "popularity": "high"
        },
        "topk": 1,
        "explain": true
      },
      "preconditions": [
        "t21_search_activity_city5 completed"
      ]
    },
    {
      "id": "t23_generate_final_answers",
      "type": "primitive",
      "name": "Generate final trip plan summary",
      "orchestration_action": "generate_final_answers",
      "parameters": {
        "processed_data": [
          "results_of_t2_rank_cities",
          "results_of_t4_select_hotel_city1",
          "results_of_t6_select_activity_city1",
          "results_of_t8_select_hotel_city2",
          "results_of_t10_select_activity_city2",
          "results_of_t12_select_hotel_city3",
          "results_of_t14_select_activity_city3",
          "results_of_t16_select_hotel_city4",
          "results_of_t18_select_activity_city4",
          "results_of_t20_select_hotel_city5",
          "results_of_t22_select_activity_city5"
        ],
        "original_question": "plan my trip to Thailand, find the top 5 cities and find me the best hotel in each city also find the best activity nearby the selected hotel for each city, plan for oct 1 to oct 30 2025 for 1 adult please use USD",
        "format": "markdown"
      },
      "preconditions": [
        "t6_select_activity_city1 completed",
        "t10_select_activity_city2 completed",
        "t14_select_activity_city3 completed",
        "t18_select_activity_city4 completed",
        "t22_select_activity_city5 completed"
      ]
    }
  ]
}



def get_constraint_prompt(problem):

    prompt = """You are an expert in understanding an input problem and generating set of constraints. Analyze the input problem and extract all relevant instance-specific constraints and contextual details necessary for accurate and feasible planning.

    Problem Statement:
    {problem}

    Provide your analysis in the following JSON format:

    {
        "constraints": ["List of specific constraints extracted from the problem"],
        "contextual_details": ["List of important contextual information that affects planning"],
        "feasibility_considerations": ["List of factors that impact the feasibility of potential solutions"]
    }

    """
    
    return prompt.replace("{problem}", problem)



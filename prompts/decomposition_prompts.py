"""
Optimised prompt generator for the o3-mini HTN Planner.

Key improvements
----------------
1. DRY: removed duplicate theory / rule blocks.
2. Modular templates for easy maintenance.
3. Dynamic tool section: injects only the requested tools.
4. The original, full OUTPUT FORMAT section is preserved verbatim.
"""

from typing import List, Optional
from prompts.tool_selector_prompt import tools as tools_desc


# ────────────────────────────────────────────────────────────
# Helper: build the tool block only for requested tools
# ────────────────────────────────────────────────────────────
def _build_tools_section(tools: List[str]):
    """
    Returns:
        tool_names_str  – the list for ORCHESTRATION ACTION SET.
        tools_desc_str  – the human-readable details for those tools.
    """
    if not tools:
        return "", ""

    name_fragments, detail_lines = [], []

    for key in tools:
        spec = tools_desc.get(key)
        if not spec:
            continue

        name_fragments.append(f"\"{spec['tool_name']}\"")

        detail_lines.append(f"- \"{spec['tool_name']}\": {spec['tool_description']}")
        if spec.get("endpoint"):
            detail_lines.append("  Available endpoints:")
            for ep in spec["endpoint"]:
                detail_lines.append(
                    f"  - `{ep['type']} {ep['endpoint_description']}`"
                )
        detail_lines.append("")  # blank line between tools

    tool_names_str = "    " + ",\n    ".join(name_fragments) + ",\n" if name_fragments else ""
    tools_desc_str = "\n".join(detail_lines)
    return tool_names_str, tools_desc_str


# ────────────────────────────────────────────────────────────
# Single-source text snippets (no duplication)
# ────────────────────────────────────────────────────────────
HTN_ESSENTIALS = """
# HTN ESSENTIALS
- Tasks: Compound (decomposable) or Primitive (directly executable).
- Methods: Recipes that break Compound tasks into ordered / unordered subtasks.
- Primitive tasks *must* map 1-to-1 to an orchestration_action.
"""

PRECONDITION_RULES = """
# PRECONDITION RULES
Specify only the minimal, direct dependencies:
- Use "task_id completed" or explicit references like
  results_of_TASK_ID.data.key_name[0].sub_property
"""

PARAMETER_REFERENCE_RULES = """
# PARAMETER REFERENCING
Allowed formats:
  • results_of_TASK_ID
  • FIELD_from_TASK_ID
  • results_of_TASK_ID.data.key[index].sub_property
Never use template placeholders such as {{variable}}.
"""

STEP_BY_STEP = """
# STEP-BY-STEP
1. Decompose the user goal → Compound tasks → Primitive tasks.
2. Order subtasks (`ordered` with integer `order` or `unordered`).
3. Ensure every leaf is primitive and has an orchestration_action + parameters.
4. Fill parameters. Generate a self-contained `prompt` for actions that need it.
5. Add minimal preconditions (see rules).
6. Validate plan: only primitive tasks in "tasks"; JSON must be parsable.
7. If required info is missing, ask ONE clarifying question (plain text).
"""

OUTPUT_FORMAT = """
# IMPORTANT JSON OUTPUT RULES

**A. Do NOT generate any comments inside the JSON output.**
This includes both single-line and block comments, such as:
- // This is a comment
- /* This is a block comment */
- Any text like: "/* ---------- City 5 Activity Tasks ---------- */" or "/* ------------------------------------------------- */"

**B. The JSON output must be strictly valid and directly parsable using the Python `json` library.**
- Do not include any comments, trailing commas, or extra text outside the JSON object.
- Only output the JSON object itself, with correct syntax and structure.

# OUTPUT FORMAT
EITHER:
  A) If the user query provides sufficient information to create a complete and detailed HTN plan (including necessary parameters for all primitive actions), return ONLY a VALID JSON object with a "tasks" key, adhering strictly to the specified fields for primitive tasks. The "methods" key should be omitted.
  OR
  B) If the user query is ambiguous or lacks specific details needed to define tasks, methods, or parameters for orchestration actions, return ONLY plain text asking the user a specific question to clarify the missing information. Do NOT return any JSON in this case. Focus the question on the *minimal* information needed to proceed.

JSON Structure (If generating plan):
{
  "tasks": [ // This list MUST ONLY contain PRIMITIVE tasks
    {
      "id": "unique_primitive_task_id",
      "type": "primitive", // This MUST be "primitive"
      "name": "Human-readable task name",
      "orchestration_action": "ACTION_NAME", // This MUST be specified for primitive tasks
      "parameters": {}, // Parameters for the primitive action
      "preconditions": ["primitive_task_id_1 completed", ...] // Preconditions MUST refer to other PRIMITIVE task IDs or initial conditions
    }
    // ... more primitive tasks, no comments allowed
  ]
  // The "methods" array should NOT be included in the final output.
  // Compound tasks should NOT be included in the "tasks" array in the final output.
}

Ensure the JSON is valid and contains no extra text or explanations. Do not include any comments in the JSON output. The output must be directly parsable by the Python `json` library.
"""


# ────────────────────────────────────────────────────────────
# Main prompt builder
# ────────────────────────────────────────────────────────────
def get_decomposition_prompt(
    main_query: str,
    user_context: str = "",
    *,
    feedback: Optional[str] = None,
    plan: Optional[str] = None,
    tools: Optional[List[str]] = None,
) -> str:
    """
    Constructs the concise, non-redundant prompt for the HTN planner.
    """
    tools = tools or []
    tool_names_str, tools_desc_str = _build_tools_section(tools)

    # Previous context block
    context_section = (
        f"\n\n# PREVIOUS CONTEXT / USER ANSWERS\n{user_context}\n"
        if user_context
        else ""
    )

    # Feedback block
    feedback_block = ""
    if feedback and plan:
        feedback_block = f"""
# PREVIOUS ITERATION
From the previous iteration we obtained the following plan and feedback:

## plan:
{plan}

## feedback:
{feedback}

(Please incorporate this feedback when creating the new plan.)
"""

    # Assemble final prompt
    prompt = f"""
# ROLE
You are an HTN planner. Decompose the user request into a plan using the provided actions.

{HTN_ESSENTIALS}
{PRECONDITION_RULES}
{PARAMETER_REFERENCE_RULES}

# ORCHESTRATION ACTION SET
Available orchestration actions: [
{tool_names_str}    "generate_final_answers",
    "generate_structured_data",
    "rank_data",
    "evaluate_data",
    "select_data"
]

{tools_desc_str}

# Built-in actions (always available – brief spec)
- "generate_final_answers": Produce final answer from processed data.
- "generate_structured_data": Convert input to structured file (json/csv/md).
- "rank_data": Identify + rank items given a generated prompt & criteria.
- "evaluate_data": Compare data; return strengths, weaknesses, assessment.
- "select_data": Select top-k options using generated prompt & criteria.

# USER QUERY
{main_query}{context_section}

{STEP_BY_STEP}
{OUTPUT_FORMAT}
{feedback_block}
"""

    # Normalise whitespace for cleanliness
    return "\n".join(line.rstrip() for line in prompt.splitlines()).lstrip()
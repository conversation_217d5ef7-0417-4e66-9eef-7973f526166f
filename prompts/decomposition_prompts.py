from typing import Optional

from prompts.tool_selector_prompt import tools as tools_desc


def get_decomposition_prompt(
    main_query: str,
    user_context: str = "",
    feedback: Optional[str] = None,
    plan: Optional[str] = None,
    tools: Optional[list[str]] = None,
) -> str:
    """
    Construct an HTN-decomposition prompt for the o3-mini model,
    applying OpenAI’s prompt-engineering best practices:

    1. Role + instructions first.
    2. Clear section delimiters (### / triple quotes).
    3. Explicit, detailed output spec.
    4. Example JSON structure shown.
    5. Precise, numbered steps (no vague language).
    6. Ask follow-up only when critical info is missing.

    Only the prompt text is modified; logic and return type remain unchanged.
    """
    if tools is None:
        tools = []

    # ------------------------------------------------------------------ #
    # Previous context from the user (if any)
    # ------------------------------------------------------------------ #
    context_section = ""
    if user_context:
        context_section = f"""
### PREVIOUS CONTEXT / USER ANSWERS
\"\"\"{user_context}\"\"\"
"""

    # ------------------------------------------------------------------ #
    # Tools list and descriptions
    # ------------------------------------------------------------------ #
    tools_desc_str = ""
    tool_names_str = ""
    for tool in tools:
        tool_key = str(tool)
        tool_info = tools_desc[tool_key]

        tool_names_str += f'    "{tool_info["tool_name"]}",\n'

        tools_desc_str += f'  - "{tool_info["tool_name"]}": {tool_info["tool_description"]}\n'
        tools_desc_str += "    Available endpoints:\n"
        for endpoint in tool_info["endpoint"]:
            tools_desc_str += f'    - `{endpoint["type"]} {endpoint["endpoint_description"]}`\n'
        tools_desc_str += "\n"

    # ------------------------------------------------------------------ #
    # Feedback from previous run (if any)
    # ------------------------------------------------------------------ #
    feedback_prompt = ""
    if feedback and plan:
        feedback_prompt = f"""
### PREVIOUS ITERATION
The following plan and feedback were produced in the prior iteration.

#### Plan
\"\"\"{plan}\"\"\"

#### Feedback
\"\"\"{feedback}\"\"\"

– Incorporate the feedback above while generating the new plan.
"""

    # ------------------------------------------------------------------ #
    # Master prompt
    # ------------------------------------------------------------------ #
    prompt = f"""
### ROLE
You are an HTN planner. Decompose the user request into a plan using the provided actions.

### CORE HTN CONCEPTS TO APPLY
-- Tasks: Units of work (Compound or Primitive).
-- Methods: Define how to achieve compound tasks using subtasks (primitive or compound).
-- Ordering: Use "ordered" (integer `order` values for sequence/parallelism) or "unordered".
-- Primitive Tasks: Must map to exactly one `orchestration_action` from the list below.

HTN planning involves recursively breaking down high-level goals via Tasks and Methods until only actionable Primitive Tasks remain.

### TASK PRECONDITION PRINCIPLES
• Explicitly list only the minimal, necessary preconditions.  
• Avoid redundant or irrelevant links.  
• Reference other tasks’ outputs with `results_of_<TASK_ID>` or `<FIELD>_from_<TASK_ID>`.

### ORCHESTRATION ACTION SET
Available orchestration actions:
[
{tool_names_str}    "generate_final_answers",
    "generate_structured_data",
    "rank_data",
    "evaluate_data",
    "select_data"
]
Every primitive task must be assigned one action.

{tools_desc_str}
  - "generate_final_answers": Generate answers from processed data & original question.  
  - "generate_structured_data": Produce structured files (json/csv/markdown).  
  - "rank_data": Identify & rank items. Must supply a self-contained `prompt`.  
  - "evaluate_data": Evaluate options against `criteria`.  
  - "select_data": Select best items. Must supply a self-contained `prompt`.

### USER QUERY
\"\"\"{main_query}\"\"\"
{context_section}{feedback_prompt}
### STEP-BY-STEP INSTRUCTIONS
1. Hierarchical Decomposition – break the goal into compound → primitive tasks.  
2. Logical & Parallel Structure – assign `order` values; choose `ordered`/`unordered`.  
3. Decompose fully to Primitive Tasks.  
4. Assign orchestration actions & parameters.  
   • For tools needing a `prompt` param (`rank_data`, `select_data`), generate a clear, self-contained instruction string.  
5. Define explicit preconditions.  
6. Validate plan integrity: only primitive tasks in final JSON.  
7. Verify & correct; restart if any rule is unmet.  
8. Missing info? Ask ONE focused follow-up question instead of JSON.

### IMPORTANT JSON OUTPUT RULES
A. No comments inside JSON.  
B. JSON must be valid & parsable via `json` in Python.

### OUTPUT FORMAT
Return ONLY ONE of the following:  
A) A valid JSON object with a "tasks" array of primitive tasks.  
B) A single clarifying question (plain text) if crucial details are missing.
"""
    return prompt
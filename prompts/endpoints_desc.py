tools = {
    27: {
      "tool_name": "Google Maps",
      "inputs": {
        "origin": "string",
        "destination": "string",
        "mode": "enum(walk, drive, transit)",
        "search_query": "string (e.g. 'nearest grocery store')",
        "radius_km": "integer"
      },
      "output": {
        "locations_list": "array",
        "directions_list": "array",
        "travel_time": "string",
        "navigation_link": "url"
      }
    },
    4: {
      "tool_name": "Lidl",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    5: {
      "tool_name": "<PERSON>di",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    34: {
      "tool_name": "Carrefour",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    10: {
      "tool_name": "Walmart",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    11: {
      "tool_name": "Costco",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    12: {
      "tool_name": "Target",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    9: {
      "tool_name": "Amazon",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    2: {
      "tool_name": "Rakuten",
      "inputs": {
        "search_query": "string (partner store or product)"
      },
      "output": {
        "cash_back_offers_list": "array",
        "redirect_urls": "array"
      }
    },
    15: {
      "tool_name": "eBay",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    3: {
      "tool_name": "MediaMarkt",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    14: {
      "tool_name": "Best Buy",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    13: {
      "tool_name": "Home Depot",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    35: {
      "tool_name": "Leroy Merlin",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    37: {
      "tool_name": "ManoMano",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    6: {
      "tool_name": "Zara",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    7: {
      "tool_name": "H&M",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    8: {
      "tool_name": "TJ Maxx",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    18: {
      "tool_name": "Vinted",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    19: {
      "tool_name": "Zalando",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    40: {
      "tool_name": "Nike",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    41: {
      "tool_name": "Adidas",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    42: {
      "tool_name": "Decathlon",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    36: {
      "tool_name": "Louis Vuitton",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    38: {
      "tool_name": "Dior",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    39: {
      "tool_name": "Gucci",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "product_list": "array"
      }
    },
    16: {
      "tool_name": "Uber",
      "inputs": {
        "pickup_location": "string",
        "dropoff_location": "string"
      },
      "output": {
        "ride_options": "array (with eta, price, vehicle type)"
      }
    },
    17: {
      "tool_name": "Uber Eats",
      "inputs": {
        "delivery_address": "string",
        "search_query": "string (e.g. 'pizza' or 'Walgreens')"
      },
      "output": {
        "restaurant_or_store_list": "array"
      }
    },
    1: {
      "tool_name": "Zillow",
      "inputs": {
        "location": "string",
        "filters": "object (e.g. {for_sale: true, price_max: 500000})"
      },
      "output": {
        "property_listings": "array"
      }
    },
    32: {
      "tool_name": "Immoweb",
      "inputs": {
        "location": "string",
        "filters": "object (e.g. {for_rent: true, budget_max: 1200})"
      },
      "output": {
        "property_listings": "array"
      }
    },
    24: {
      "tool_name": "Google Flights",
      "inputs": {
        "origin_airport": "string",
        "destination_airport": "string",
        "departure_date": "date",
        "return_date": "date"
      },
      "output": {
        "flight_options_list": "array"
      }
    },
    25: {
      "tool_name": "Google Hotels",
      "inputs": {
        "location": "string",
        "checkin_date": "date",
        "checkout_date": "date"
      },
      "output": {
        "hotel_list": "array"
      }
    },
    29: {
      "tool_name": "Search Engines (SERP)",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "search_results_list": "array (with title, url, snippet)"
      }
    },
    26: {
      "tool_name": "Google Images",
      "inputs": {
        "search_query": "string",
        "reverse_image_url": "url"
      },
      "output": {
        "image_results_list": "array"
      }
    },
    28: {
      "tool_name": "Google Scholar",
      "inputs": {
        "search_query": "string (author, title, or keywords)"
      },
      "output": {
        "article_list": "array (with citation info)"
      }
    },
    30: {
      "tool_name": "Science Repositories",
      "inputs": {
        "search_query": "string (paper ID, author, keywords)"
      },
      "output": {
        "paper_list": "array"
      }
    },
    31: {
      "tool_name": "YouTube",
      "inputs": {
        "search_query": "string"
      },
      "output": {
        "video_list": "array"
      }
    },
    21: {
      "tool_name": "Coding Agent",
      "inputs": {
        "prompt": "string (task description)",
        "code_snippet": "string (optional)",
        "language": "string"
      },
      "output": {
        "generated_code": "string",
        "explanation": "string"
      }
    },
    22: {
      "tool_name": "Stack Overflow",
      "inputs": {
        "search_query": "string (error message or question)"
      },
      "output": {
        "question_and_answer_list": "array"
      }
    },
    23: {
      "tool_name": "GitHub",
      "inputs": {
        "repository_url": "url",
        "search_query": "string (e.g. for issues)"
      },
      "output": {
        "repository_info": "object",
        "issue_list": "array",
        "file_content": "string"
      }
    },
    20: {
      "tool_name": "Cars",
      "inputs": {
        "search_query": "string (e.g. 'Toyota Camry')",
        "filters": "object (e.g. {year: 2022, max_mileage: 50000})"
      },
      "output": {
        "car_listings": "array"
      }
    }
  }
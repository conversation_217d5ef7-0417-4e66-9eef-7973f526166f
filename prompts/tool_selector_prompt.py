import json

# tools = {
#   # // ─── GEO / NAVIGATION ────────────────────────────────────────────
#   27: {"tool_name": "Google Maps", "tool_description": "Geospatial look-up: find store locations within X km, directions, travel time (walk / drive / transit), nearest branch, and navigation links."},

#   # // ─── GROCERY & MASS RETAIL ──────────────────────────────────────
#   4:  {"tool_name": "Lidl",     "tool_description": "European discount supermarket. Weekly specials, fresh produce, and store locator. this tool can be used to find products in lidl stores, this store can be online and physical"},
#   5:  {"tool_name": "Al<PERSON>",     "tool_description": "Global discount grocer with private-label goods; online flyer + branch finder. this tool can be used to find products in aldi stores. this store can be online and physical"},
#   34: {"tool_name": "<PERSON><PERSON><PERSON>","tool_description": "Multi-format supermarket / hypermarket chain; in-store or online grocery ordering. this tool can be used to find products in carrefour stores. this store can be online and physical"},
#   10: {"tool_name": "Walmart",  "tool_description": "US-centric hypermarket. Groceries, general merchandise, curb-side pickup, delivery. this tool can be used to find products in walmart stores. this store can be online and physical"},
#   11: {"tool_name": "Costco",   "tool_description": "Membership warehouse club: bulk groceries, household items; limited locations. this tool can be used to find products in costco stores. this store can be online and physical"},
#   12: {"tool_name": "Target",   "tool_description": "US retail chain; groceries + general goods; Drive-Up & same-day options., this tool can be used to find products in target stores. this store can be online and physical"},

#   # // ─── GENERAL ONLINE SHOPPING / PRICE AGGREGATORS ───────────────
#   9:  {"tool_name": "Amazon",   "tool_description": "Everything store. Pantry / Fresh in select regions; ratings & price history., this is an store only"},
#   2:  {"tool_name": "Rakuten",  "tool_description": "Cash-back & coupon aggregator that redirects to partner stores. this tool can be used to find products in rakuten stores. this store can be online and physical"},
#   15: {"tool_name": "eBay",     "tool_description": "Auction & fixed-price listings—new or second-hand goods. this tool can be used to find products in ebay stores. this store can be online and physical"},

#   # // ─── ELECTRONICS & DIY ──────────────────────────────────────────
#   3:  {"tool_name": "MediaMarkt","tool_description": "EU electronics retailer: TVs, PCs, phones; click-&-collect."},
#   14: {"tool_name": "Best Buy", "tool_description": "US electronics chain; store pickup, price match."},
#   13: {"tool_name": "Home Depot","tool_description": "DIY / construction supplies; project calculators, rental."},
#   35: {"tool_name": "Leroy Merlin","tool_description": "EU DIY & gardening retailer; bulk material quotes."},
#   37: {"tool_name": "ManoMano", "tool_description": "Online marketplace for DIY, gardening, plumbing, electrical."},

#   # // ─── FASHION / APPAREL ──────────────────────────────────────────
#   6:  {"tool_name": "Zara",     "tool_description": "Fast-fashion clothing & accessories; online stock checker."},
#   7:  {"tool_name": "H&M",      "tool_description": "Global fast-fashion brand; style picks & sustainability line."},
#   8:  {"tool_name": "TJ Maxx",  "tool_description": "Off-price fashion & home goods; treasure-hunt deals."},
#   18: {"tool_name": "Vinted",   "tool_description": "Peer-to-peer second-hand fashion marketplace."},
#   19: {"tool_name": "Zalando",  "tool_description": "EU online fashion & lifestyle shop; extensive filtering."},
#   40: {"tool_name": "Nike",     "tool_description": "Athletic brand; custom shoes ('By You') & store list."},
#   41: {"tool_name": "Adidas",   "tool_description": "Sportswear; member-exclusive drops, outlet filter."},
#   42: {"tool_name": "Decathlon","tool_description": "Value-priced sporting goods; real-time stock per store."},
#   36: {"tool_name": "Louis Vuitton","tool_description": "Luxury leather goods; limited physical boutiques."},
#   38: {"tool_name": "Dior",     "tool_description": "Luxury fashion & beauty; find nearest maison."},
#   39: {"tool_name": "Gucci",    "tool_description": "Luxury fashion house; collect in store or courier."},

#   # // ─── RIDE / DELIVERY ────────────────────────────────────────────
#   16: {"tool_name": "Uber",     "tool_description": "Ride-hailing to/from selected locations."},
#   17: {"tool_name": "Uber Eats","tool_description": "Restaurant & convenience delivery within service radius."},

#   # // ─── REAL ESTATE ────────────────────────────────────────────────
#   1:  {"tool_name": "Zillow",   "tool_description": "US real estate: buy, rent, Zestimate, filters & maps."},
#   32: {"tool_name": "Immoweb",  "tool_description": "Belgian property marketplace; region / budget search."},

#   # // ─── TRAVEL & LODGING ───────────────────────────────────────────
#   24: {"tool_name": "Google Flights","tool_description": "Price graph, date grid, fare alerts, multi-city."},
#   25: {"tool_name": "Google Hotels","tool_description": "Compare hotels, filters: price, rating, distance to POI."},

#   # // ─── INFO & RESEARCH ────────────────────────────────────────────
#   29: {"tool_name": "Search Engines (SERP)","tool_description": "Raw Google/Bing/Brave result pages for general web search."},
#   26: {"tool_name": "Google Images","tool_description": "Reverse-image and visual similarity search."},
#   28: {"tool_name": "Google Scholar","tool_description": "Academic articles, citations, h-index metrics."},
#   30: {"tool_name": "Science Repositories","tool_description": "Direct access to arXiv, IEEE Xplore, PubMed, etc."},
#   31: {"tool_name": "YouTube",  "tool_description": "Video tutorials, reviews, unboxings, how-to guides."},

#   # // ─── DEV / PROGRAMMING ──────────────────────────────────────────
#   21: {"tool_name": "Coding Agent","tool_description": "Autonomous coding assistant: write / debug / refactor code."},
#   22: {"tool_name": "Stack Overflow","tool_description": "Crowd-sourced Q&A; canonical bug explanations."},
#   23: {"tool_name": "GitHub",   "tool_description": "Code hosting: repos, issues, release assets."},

#   # // ─── VEHICLES ───────────────────────────────────────────────────
#   20: {"tool_name": "Cars",     "tool_description": "Auto classifieds or rental aggregators (Cars.com, etc.)."}
# }

tools = {
  "27": {
    "tool_name": "Google Maps",
    "tool_description": "Geospatial look-up and navigation: This tool can retrieve routes and directions between two points (A and B) for various modes of transportation, including driving, walking, cycling, and public transit (such as metro, bus, train, and tram). It provides estimated travel times, step-by-step navigation, and public transport schedules. Additionally, it can find nearby locations, points of interest, businesses (e.g., stores, restaurants), and specific branches within a specified radius or area, offering details like addresses, opening hours, contact information, and user reviews. It also provides navigation links to open directions directly in Google Maps.",
    "port": 9284,
    "endpoint": [
      {
        "type": "GET",
        "endpoint_description": "/ - Provides API information and usage examples for location-based services"
      },
      {
        "type": "GET",
        "endpoint_description": "/places/search - Searches for places and activities near locations. Examples: \"restaurants in Paris\", \"hiking spots in Texas\", \"museums in London\", \"coffee shops near Times Square\". Supports filtering by place type, price range, operating hours, and radius"
      },
      {
        "type": "GET",
        "endpoint_description": "/places/details/{place_id} - Retrieves comprehensive details for a specific place including contact information, operating hours, reviews, photos, website, and real-time data like current busy levels"
      },
      {
        "type": "GET",
        "endpoint_description": "/directions - Calculates routes and directions between locations with multiple transportation modes (driving, walking, transit, cycling). Supports waypoints, route alternatives, and traffic avoidance options"
      }
    ]
  },
  "4": {
    "tool_name": "Lidl",
    "tool_description": "European discount supermarket focused on groceries but also selling other items like electronics and clothes. This tool can access the online catalog to search for products, gather details including images, prices, reviews, and availability, and will indicate if the product is generally available in their physical stores.",
    "port": 9295,
    "endpoint": [
      {
        "type": "GET",
        "endpoint_description": "/ - Displays API manual and usage instructions for Lidl product search"
      },
      {
        "type": "GET",
        "endpoint_description": "/search - Searches Lidl's product range for groceries, household items, and weekly special offers. Returns product information, prices, nutritional data, and availability across different European countries"
      }
    ]
  },
  "5": {
    "tool_name": "Aldi",
    "tool_description": "Global discount supermarket chain focusing on private-label groceries and weekly special buys. This tool can access the online catalog to search for products, gather details including images, prices, current promotions, and availability, and will indicate if the product is generally available in their physical stores.",
    "port": 9289,
    "endpoint": [
      {
        "type": "GET",
        "endpoint_description": "/ - Displays API manual and usage instructions for Aldi product search tool"
      },
      {
        "type": "GET",
        "endpoint_description": "/search - Searches Aldi's product catalog for groceries, household items, and special offers. Returns product names, prices, descriptions, availability, and store locations. Supports country-specific searches and result limiting"
      }
    ]
  },
  "34": {
    "tool_name": "Carrefour",
    "tool_description": "International multi-format retailer operating hypermarkets and supermarkets offering groceries, electronics, and general merchandise. This tool can access the online catalog to search for products, gather details including images, prices, reviews, and check availability for both in-store and online shopping options.",
    "port": "",
    "endpoint": []
  },
  "10": {
    "tool_name": "Walmart",
    "tool_description": "US-centric multinational hypermarket offering a vast selection of groceries and general merchandise. Stores are primarily large physical locations but have a robust online presence. This tool can access the online catalog to search for products, gather details including images, prices, and user reviews, and will indicate availability for in-store pickup or delivery.",
    "port": 9299,
    "endpoint": [
      {
        "type": "GET",
        "endpoint_description": "/ - Displays comprehensive API information and usage help for Walmart product services"
      },
      {
        "type": "POST",
        "endpoint_description": "/product - Scrapes detailed information from a single Walmart product URL including specifications, reviews, pricing, availability, and related products"
      },
      {
        "type": "POST",
        "endpoint_description": "/search - Searches Walmart's vast catalog and scrapes multiple product results. Returns product details, prices, ratings, availability, shipping options, and store pickup information"
      }
    ]
  },
  "11": {
    "tool_name": "Costco",
    "tool_description": "Membership-only warehouse club known for selling products in bulk, from groceries to electronics and household items. While primarily operating large physical warehouses, this tool can access the online catalog to search for products, gather details including images, prices, and member-exclusive deals, and will indicate if items are available for warehouse pickup.",
    "port": 9291,
    "endpoint": [
      {
        "type": "GET",
        "endpoint_description": "/ - Displays API manual and usage guidelines for Costco product search functionality"
      },
      {
        "type": "GET",
        "endpoint_description": "/search - Searches Costco's product catalog for bulk items, electronics, groceries, and member-exclusive deals. Returns product information including bulk pricing, member prices, availability, and warehouse locations"
      }
    ]
  },
  "12": {
    "tool_name": "Target",
    "tool_description": "US-based general merchandise retailer offering apparel, home goods, electronics, and groceries. Stores are physical but tightly integrated with a strong online platform. This tool can access the online catalog to search for products, gather details including images, prices, and check availability for same-day delivery, Drive-Up, or in-store pickup.",
    "port": 9297,
    "endpoint": [
      {
        "type": "GET",
        "endpoint_description": "/search - Searches Target's extensive product catalog including clothing, electronics, home goods, groceries, and seasonal items. Returns product information, prices, availability, store locations, and online/in-store pickup options"
      }
    ]
  },
  "9": {
    "tool_name": "Amazon",
    "tool_description": "Everything store. This tool can search Amazon's vast online catalog to retrieve comprehensive product listings including images, prices, detailed descriptions, customer reviews, ratings, and shipping information. This is an online-only store.",
    "port": 9280,
    "endpoint": [
      {
        "type": "GET",
        "endpoint_description": "/ - Provides comprehensive API information, available endpoints, and usage examples for the Amazon product scraping service"
      },
      {
        "type": "POST",
        "endpoint_description": "/search - Searches and scrapes Amazon products based on search queries. Returns detailed product information including titles, prices, ratings, descriptions, images, and availability. Supports filtering by price range, ratings, and result limits. Requires `query`, `max_results`, `stats_threshold`, and `zyte_api_key` parameters"
      }
    ]
  },
  "2": {
    "tool_name": "Rakuten",
    "tool_description": "French online marketplace offering a wide range of new and used goods from third-party sellers. This tool is specifically configured to search for products available on the French market, allowing users to gather details including images, check prices, view seller information, and access available customer reviews. This is primarily an online marketplace.",
    "port": "",
    "endpoint": []
  },
  "15": {
    "tool_name": "eBay",
    "tool_description": "Auction & fixed-price listings—new or second-hand goods. This tool can be used to find products on eBay, providing listings with images, prices, seller details, and buyer reviews. Products may be offered by online-only sellers or sellers with physical stores, and this will be indicated where possible.",
    "port": "",
    "endpoint": []
  },
  "3": {
    "tool_name": "MediaMarkt",
    "tool_description": "EU electronics retailer: TVs, PCs, phones. This tool can search their online catalog for products, providing details including images, prices, specifications, and customer reviews, with an option to check for click-and-collect availability at physical stores.",
    "port": "",
    "endpoint": []
  },
  "14": {
    "tool_name": "Best Buy",
    "tool_description": "US electronics chain. This tool can search their online catalog for products, providing detailed listings with images, prices, specifications, and customer reviews, with options for store pickup and price matching.",
    "port": "",
    "endpoint": []
  },
  "13": {
    "tool_name": "Home Depot",
    "tool_description": "DIY / construction supplies. This tool can search their online catalog for products, providing details including images, prices, and specifications, along with information on project calculators and equipment rental, indicating availability for in-store pickup.",
    "port": 9293,
    "endpoint": [
      {
        "type": "GET",
        "endpoint_description": "/ - Displays welcome message and API information for Home Depot product search"
      },
      {
        "type": "GET",
        "endpoint_description": "/search - Searches Home Depot's catalog for tools, building materials, appliances, and home improvement products. Returns product specifications, prices, availability, store locations, and installation service options"
      }
    ]
  },
  "35": {
    "tool_name": "Leroy Merlin",
    "tool_description": "EU DIY & gardening retailer. This tool can search their online catalog for products, providing details including images, prices, and specifications, with options for bulk material quotes and in-store availability checks.",
    "port": "",
    "endpoint": []
  },
  "37": {
    "tool_name": "ManoMano",
    "tool_description": "Online marketplace for DIY, gardening, plumbing, electrical. This tool can search for products across various sellers, providing listings with images, prices, and seller information. This is an online-only marketplace.",
    "port": "",
    "endpoint": []
  },
  "6": {
    "tool_name": "Zara",
    "tool_description": "Fast-fashion clothing & accessories. This tool can search their online catalog for products, providing details including images, prices, and size availability, with an online stock checker for physical store locations.",
    "port": "",
    "endpoint": []
  },
  "7": {
    "tool_name": "H&M",
    "tool_description": "Global fast-fashion brand. This tool can search their online catalog for products, providing details including images, prices, and style recommendations, with information on their sustainability line and store availability.",
    "port": "",
    "endpoint": []
  },
  "8": {
    "tool_name": "TJ Maxx",
    "tool_description": "Off-price fashion & home goods. This tool can search their online catalog for products, providing details including images and prices, reflecting their treasure-hunt deals, and can indicate general in-store availability (though specific stock may vary).",
    "port": "",
    "endpoint": []
  },
  "18": {
    "tool_name": "Vinted",
    "tool_description": "Peer-to-peer second-hand fashion marketplace. This tool can search for used fashion items, providing listings with user-uploaded images, prices, and seller ratings. This is an online-only platform for individual sellers.",
    "port": "",
    "endpoint": []
  },
  "19": {
    "tool_name": "Zalando",
    "tool_description": "EU online fashion & lifestyle shop. This tool can search their extensive catalog for products, providing detailed listings with images, prices, brand information, and customer reviews, with robust filtering options. This is an online-only store.",
    "port": 9300,
    "endpoint": [
      {
        "type": "GET",
        "endpoint_description": "/ - Provides API manual and usage instructions for European fashion search"
      },
      {
        "type": "GET",
        "endpoint_description": "/search - Searches Zalando's fashion catalog for clothing, shoes, and accessories across European markets. Returns product information, prices, size availability, brand details, customer reviews, and country-specific shipping options. Supports gender-specific and domain-specific searches"
      }
    ]
  },
  "40": {
    "tool_name": "Nike",
    "tool_description": "Athletic brand. This tool can search their online catalog for products, providing detailed listings with images, prices, and customer reviews, including information on custom shoes ('By You') and a list of physical store locations.",
    "port": 9296,
    "endpoint": [
      {
        "type": "GET",
        "endpoint_description": "/ - Provides tool manual and API usage guidelines for Nike product search"
      },
      {
        "type": "GET",
        "endpoint_description": "/search - Searches Nike's global catalog for athletic footwear, apparel, and equipment across multiple regions. Returns product details, prices in local currencies, size availability, color options, and regional-specific product variations"
      }
    ]
  },
  "41": {
    "tool_name": "Adidas",
    "tool_description": "Sportswear. This tool can search their online catalog for products, providing detailed listings with images, prices, and customer reviews, including information on member-exclusive drops and outlet filters, and can indicate physical store locations.",
    "port": "",
    "endpoint": []
  },
  "42": {
    "tool_name": "Decathlon",
    "tool_description": "Value-priced sporting goods. This tool can search their online catalog for products, providing detailed listings with images, prices, and real-time stock information per physical store.",
    "port": "",
    "endpoint": []
  },
  "36": {
    "tool_name": "Louis Vuitton",
    "tool_description": "Luxury leather goods. This tool can search their online catalog for products, providing high-quality images and prices, and can help locate their limited physical boutiques.",
    "port": "",
    "endpoint": []
  },
  "38": {
    "tool_name": "Dior",
    "tool_description": "Luxury fashion & beauty. This tool can search their online catalog for products, providing high-quality images and prices, and can help users find their nearest maison (physical store).",
    "port": "",
    "endpoint": []
  },
  "39": {
    "tool_name": "Gucci",
    "tool_description": "Luxury fashion house. This tool can search their online catalog for products, providing high-quality images and prices, and can indicate options for collecting in-store or courier delivery.",
    "port": "",
    "endpoint": []
  },
  "16": {
    "tool_name": "Uber",
    "tool_description": "Ride-hailing service that can search for available rides from point A to point B. It provides estimated arrival times and allows access to general driver information, including their overall rating and compliments from other riders, similar to the Uber app interface.",
    "port": "",
    "endpoint": []
  },
  "17": {
    "tool_name": "Uber Eats",
    "tool_description": "Food and convenience delivery service. This tool can gather product menus, including item details and pricing, from restaurants and stores available within a specified service radius. It can also access information regarding online store availability and operating hours within the Uber Eats platform.",
    "port": 9298,
    "endpoint": [
      {
        "type": "CLI",
        "endpoint_description": "UberEats uses a command-line interface rather than traditional API endpoints. The service provides restaurant and menu scraping capabilities through the `UberEatsScraper` class with methods for searching restaurants and retrieving detailed menu information including prices, descriptions, and availability."
      }
    ]
  },
  "1": {
    "tool_name": "Zillow",
    "tool_description": "US real estate marketplace for buying, selling, and renting properties. This tool can search for houses, apartments, condos, and other property types across the US, providing comprehensive listings that include high-resolution images, detailed descriptions, 'Zestimate' property valuations, current prices, rental rates, floor plans (if available), virtual tours, neighborhood information, school ratings, and historical sales data. Users can filter searches by various parameters such as price, number of bedrooms/bathrooms, square footage, property type, and specific amenities (e.g., pool, garage). It can also indicate if a property is for sale by owner, new construction, or a foreclosure. Note: Zillow primarily covers the US market.",
    "port": "",
    "endpoint": []
  },
  "32": {
    "tool_name": "Immoweb",
    "tool_description": "Belgium's leading property marketplace for buying, selling, and renting real estate. This tool can search for apartments, houses, land, and commercial properties across Belgium. It provides detailed listings with images, prices, rental rates, property descriptions, number of bedrooms/bathrooms, surface area (m²), energy performance certificates (EPB), and location details. Users can filter by region, budget, property type, and specific features (e.g., terrace, garden). It can also indicate if a property is new construction or part of a real estate project.",
    "port": 9294,
    "endpoint": [
      {
        "type": "GET",
        "endpoint_description": "/ - Provides API manual and usage guidelines for Belgian real estate search"
      },
      {
        "type": "GET",
        "endpoint_description": "/search - Searches Immoweb for properties in Belgium including houses, apartments, commercial spaces, and land. Returns property details, prices, locations, photos, energy ratings, and contact information for real estate agents"
      }
    ]
  },
  "24": {
    "tool_name": "Google Flights",
    "tool_description": "This tool searches for flights and provides information such as prices, date grids, and allows for multi-city searches. It can also set fare alerts. To use this tool effectively, you can specify: origin, destination, earliest and latest departure/return dates, minimum and maximum length of stay, preferred airlines, maximum duration, maximum price, currency, maximum number of stops, desired departure/arrival times (by hour), number of carry-on/checked bags, specific trip days (e.g., weekend), seating classes (e.g., ECONOMY_CLASS, BUSINESS_CLASS), and the number of adult, child, or infant passengers. It can return the cheapest flights or sort by best match. One-way flights can also be searched.",
    "port": 9282,
    "endpoint": [
      {
        "type": "GET",
        "endpoint_description": "/ - Provides API documentation, usage examples, and information about flight search capabilities"
      },
      {
        "type": "GET",
        "endpoint_description": "/flights/search - Searches for flights between airports with flexible options. Supports one-way and round-trip searches, passenger counts (adults, children, infants), seat preferences, maximum stops, and date ranges. Returns flight details including prices, airlines, duration, and booking links"
      }
    ]
  },
  "25": {
    "tool_name": "Google Hotels",
    "tool_description": "This tool allows you to search for and compare hotels. You can filter by price, user rating, distance to points of interest, and various amenities (e.g., free Wi-Fi, pool, fitness center, pet-friendly). You can also specify accommodation types (e.g., beach_hotel, resort, hostel, apartment), hotel class (star rating from 0 to 5), and the number of adults and children, including their ages. When searching, you can provide a query (e.g., 'cheap hotel near Eiffel Tower in Paris with free wifi'), check-in and check-out dates, or a length of stay. You can also specify a maximum check-in date, currency, and minimum/maximum prices. Results can be sorted by cheapest price, either nightly or total stay price.",
    "port": 9283,
    "endpoint": [
      {
        "type": "GET",
        "endpoint_description": "/ - Displays comprehensive API documentation with examples for hotel search and booking services"
      },
      {
        "type": "GET",
        "endpoint_description": "/hotels/search - Searches for hotels in specified destinations with check-in/check-out dates, guest counts, currency preferences, and provider filtering. Returns hotel listings with prices, ratings, amenities, and availability"
      },
      {
        "type": "GET",
        "endpoint_description": "/hotels/details/{hotel_id} - Retrieves detailed information for a specific hotel including room types, amenities, policies, photos, reviews, and real-time pricing for specified dates and guest counts"
      }
    ]
  },
  "29": {
    "tool_name": "Search Engines (SERP)",
    "tool_description": "This tool performs general web searches using Google, Bing, or Brave, returning raw search engine results pages (SERP). It can be used for a wide range of informational queries, fact-checking, and finding specific web pages. You can provide a search query, and it will return relevant snippets, titles, and URLs from across the internet.",
    "port": 9281,
    "endpoint": [
      {
        "type": "GET",
        "endpoint_description": "/ - Displays API health status, available endpoints, detailed payload formats, and usage examples for multi-engine web scraping"
      },
      {
        "type": "GET",
        "endpoint_description": "/engines - Returns a list of all available search engines (Google, Bing, Brave, etc.) that can be used for search operations"
      },
      {
        "type": "POST",
        "endpoint_description": "/search - Performs concurrent searches across multiple search engines (Google, Bing, Brave) and aggregates results. Extracts main content from web pages, handles deduplication, and provides balanced results from each engine. Supports custom API keys for Brave and Zyte proxy services"
      }
    ]
  },
  "26": {
    "tool_name": "Google Images",
    "tool_description": "This tool performs image searches, including reverse image search and visual similarity search. You can provide a query or an image URL, and it will return visually similar images or images related to the query. It's useful for identifying objects, places, or people in images, finding the source of an image, or discovering similar visual content.",
    "port": 9286,
    "endpoint": [
      {
        "type": "GET",
        "endpoint_description": "/ - Provides API information and usage examples for image search capabilities"
      },
      {
        "type": "GET",
        "endpoint_description": "/images/search - Searches for images across the web based on text queries. Returns image URLs, titles, source websites, dimensions, and file types. Supports language preferences and result limiting. Uses SerpAPI for comprehensive image search results"
      }
    ]
  },
  "28": {
    "tool_name": "Google Scholar",
    "tool_description": "This tool specializes in searching for academic articles, scholarly publications, and citations across various disciplines. It can be used to find research papers, theses, books, abstracts, and court opinions from academic publishers, professional societies, online repositories, universities, and other web sites. It also provides citation metrics, such as h-index, for authors and publications.",
    "port": 9287,
    "endpoint": [
      {
        "type": "GET",
        "endpoint_description": "/ - Displays API documentation for academic paper and research search functionality"
      },
      {
        "type": "GET",
        "endpoint_description": "/scholar/search - Searches Google Scholar for academic papers, research articles, theses, and scholarly publications. Returns paper titles, authors, publication dates, citation counts, abstracts, and PDF links. Supports PDF content processing and language preferences"
      }
    ]
  },
  "30": {
    "tool_name": "Science Repositories",
    "tool_description": "This tool provides direct access to scientific research papers and preprints from various repositories like arXiv, IEEE Xplore, PubMed, and others. It's useful for in-depth scientific research, accessing the latest findings, and exploring specific research topics within different scientific fields. You can search by keywords, author, or publication.",
    "port": "",
    "endpoint": []
  },
  "31": {
    "tool_name": "YouTube",
    "tool_description": "This tool is used for searching for YouTube videos, channels, and playlists. It can also play videos, retrieve video metadata (including title, channel name, view count, like count, publish date, and video length), and answer questions about video content using audio and visual features, or by analyzing the video's transcript. It can provide links to videos and, where available, access to comments and transcripts.",
    "port": 9285,
    "endpoint": [
      {
        "type": "GET",
        "endpoint_description": "/ - Displays API information and usage guidelines for YouTube video search functionality"
      },
      {
        "type": "GET",
        "endpoint_description": "/videos/search - Searches YouTube for videos based on queries. Returns video metadata including titles, descriptions, view counts, upload dates, channel information, thumbnails, and direct video URLs. Supports result limiting and requires optional YouTube API key for enhanced features"
      }
    ]
  },
  "21": {
    "tool_name": "Coding Agent",
    "tool_description": "An autonomous coding assistant capable of writing, debugging, and refactoring code. Given a task or project description, it can build or modify code within a sandboxed environment and return the results, including generated code, test outputs, or proposed solutions. It acts like an AI-powered IDE (Integrated Development Environment), capable of understanding coding requests and generating functional code snippets or entire projects.",
    "port": "",
    "endpoint": []
  },
  "22": {
    "tool_name": "Stack Overflow",
    "tool_description": "A crowd-sourced question and answer platform for programmers and developers. This tool can be used to search for solutions to coding errors, programming questions, and to find canonical explanations for bugs and development challenges. It provides access to a vast repository of community-contributed knowledge, including code examples and best practices.",
    "port": "",
    "endpoint": []
  },
  "23": {
    "tool_name": "GitHub",
    "tool_description": "A web-based platform for version control and collaboration, primarily for software development. This tool can access code repositories (repos), track issues, retrieve release assets, and search for code, projects, and discussions within the GitHub ecosystem. It's useful for finding open-source projects, collaborating on code, reporting bugs, and managing software releases.",
    "port": "",
    "endpoint": []
  },
  "20": {
    "tool_name": "Cars",
    "tool_description": "This tool serves as an aggregator for auto classifieds and rental platforms (e.g., Cars.com, AutoTrader, Edmunds, AutoScout24, AutoTempest). It can search for new and used vehicles for sale, as well as rental cars. For each search result, it gathers detailed information including high-resolution images, make, model, year, price, mileage, vehicle history reports (if available), specifications (engine, transmission, features), trim level, dealership or seller information, location, and user reviews where applicable. You can search by various parameters such as make, model, year range, price range, body type (e.g., SUV, sedan, truck), fuel type (e.g., electric, hybrid, gasoline), transmission type, and location.",
    "port": 9290,
    "endpoint": [
      {
        "type": "GET",
        "endpoint_description": "/ - Provides comprehensive API overview and contract information for automotive search services"
      },
      {
        "type": "GET",
        "endpoint_description": "/health - Returns API health status, system information, and service availability checks"
      },
      {
        "type": "POST",
        "endpoint_description": "/search - Searches for vehicles using specific parameters like make, model, year, price range, mileage, and location. Returns detailed car listings with prices, specifications, dealer information, and photos"
      },
      {
        "type": "POST",
        "endpoint_description": "/search/llm - Accepts natural language queries for car searches (e.g., \"reliable family SUV under $30k\") and uses LLM processing to convert them into structured search parameters before executing the search"
      }
    ]
  }
}



prompt = """
ROLE
You are “Tool Relevance Filter.”  
Your ONLY job: scan the entire tool list and remove the tools that are clearly **irrelevant** to the user’s request.

2-STEP METHOD (internal reasoning, not in output)
1. For each tool in order, silently ask:  
     “Could a normal user plausibly open this tool to satisfy ANY part of the request?”  
     • YES  → mark KEEP  
     • NO   → mark DISCARD
2. After the scan, output **all** IDs marked KEEP in their original list order.  
   (No re-ordering, no execution sequence, no dedup needed—just the surviving IDs.)

OUTPUT FORMAT (strict)
{
  "tool_IDs": [ <int>, <int>, ... ]   // IDs of all kept tools, in original order
}

RULES
1. Use ONLY numeric IDs from the provided pool.
2. If multiple tools are relevant, include them all.  
   (Example: a grocery query may KEEP every supermarket + Google Maps.)
3. If zero tools help (or the request is purely conceptual) output: { "tool_IDs": [] }
4. ABSOLUTELY NO prose, explanations, or extra keys—only the JSON object.

TOOLS
<tools>
{tools}
</tools>


EXAMPLES
• User: “Find me the cheapest 55-inch 4K TV at MediaMarkt, Amazon, and Best Buy.”
  → { "tool_IDs": [3, 9, 14] }

• User: “Order me an Uber to the nearest Lidl.”
  → { "tool_IDs": [27, 4, 16] }   ← same IDs, original order from the master list

• User: “Search for recent papers on GANs from IEEE and arXiv.”
  → { "tool_IDs": [30] }

USER REQUEST
<user_request>
{user_request}
</user_request>
"""



def get_tool_selector_prompt(tools: list[dict], user_request: str, feedback=None, selected_tools=None):
    # Safely inject the tools list and user request without triggering str.format

    feedback_prompt = """
    # PREVIOUS ITERATION
    From the previous iteration we obtained the following selected tools and feedback:

    ## selected tools:
    {selected_tools}

    ## feedback:
    {feedback}

    - to create the new plan consider the given feedback
    """
    if feedback and selected_tools:
      feedback_prompt = feedback_prompt.replace("{feedback}", feedback).replace("{selected_tools}", str(selected_tools))
    else:
      feedback_prompt = ""
    prompt = """
    ROLE
    You are “Tool Relevance Filter.”  
    Your ONLY job: scan the entire tool list and remove the tools that are clearly **irrelevant** to the user’s request.

    2-STEP METHOD (internal reasoning, not in output)
    1. For each tool in order, silently ask:  
        “Could a normal user plausibly open this tool to satisfy ANY part of the request?”  
        • YES  → mark KEEP  
        • NO   → mark DISCARD
    2. After the scan, output **all** IDs marked KEEP in their original list order.  
      (No re-ordering, no execution sequence, no dedup needed—just the surviving IDs.)

    OUTPUT FORMAT (strict)
    {
      "tool_IDs": [ <int>, <int>, ... ]   // IDs of all kept tools, in original order
    }

    RULES
    1. Use ONLY numeric IDs from the provided pool.
    2. If multiple tools are relevant, include them all.  
      (Example: a grocery query may KEEP every supermarket + Google Maps.)
    3. If zero tools help (or the request is purely conceptual) output: { "tool_IDs": [] }
    4. ABSOLUTELY NO prose, explanations, or extra keys—only the JSON object.

    TOOLS
    <tools>
    {tools}
    </tools>


    EXAMPLES
    • User: “Find me the cheapest 55-inch 4K TV at MediaMarkt, Amazon, and Best Buy.”
      → { "tool_IDs": [3, 9, 14] }

    • User: “Order me an Uber to the nearest Lidl.”
      → { "tool_IDs": [27, 4, 16] }   ← same IDs, original order from the master list

    • User: “Search for recent papers on GANs from IEEE and arXiv.”
      → { "tool_IDs": [30] }

    """+feedback_prompt+"""

    USER REQUEST
    <user_request>
    {user_request}
    </user_request>
    """

    tools_json = json.dumps(tools, indent=2)
    return prompt.replace("{tools}", tools_json).replace("{user_request}", user_request)


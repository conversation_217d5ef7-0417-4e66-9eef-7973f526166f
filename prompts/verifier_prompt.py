



def get_verifier_prompt(problem, plan, constraints):

    prompt = """
    Provide a reward score between -100 and 100 for the quality of the provided plan steps, using
    strict evaluation standards. Ensure the reward reflects how effectively the plan contributes to

    progressing toward the correct solution.

    Problem Statement:

    {problem}


    Plan:
    {plan}

    Consider the following constraints while evaluating:

    {constraints}

    you should output the following depending on either situation if you need clarification from the user asking the problem to provide qualitative feedback output the following:

    {
    'user_question': "question to ask the user about the problem for clarification"
    }

    Otherwise provide feedback in the following JSON format:

    {
        "plan_feedback": "[Step-by-step reasoning about the plan quality]",
        "tools_feedback": "[Step-by-step reasoning about the tools selection and usage]",
        "score": [Strictly provide an integer reward score between -100 and 100]
    }

    """

    return prompt.replace("{problem}", problem).replace("{plan}", str(plan)).replace("{constraints}", str(constraints))


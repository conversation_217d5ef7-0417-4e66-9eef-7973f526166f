



def get_verifier_prompt(problem, plan, constraints):

    prompt = """
    You are an expert plan verifier. Your task is to evaluate the quality of the provided plan and provide feedback.

    Problem Statement:
    {problem}

    Plan:
    {plan}

    Constraints:
    {constraints}

    EVALUATION INSTRUCTIONS:
    1. First, determine if you have enough information to properly evaluate this plan
    2. If the problem statement is vague, ambiguous, or missing critical details needed for planning, ask for clarification
    3. Otherwise, provide a detailed evaluation with a score

    WHEN TO ASK FOR CLARIFICATION:
    - If the problem lacks specific dates, locations, or requirements
    - If budget constraints are mentioned but not specified
    - If the scope is unclear (e.g., "plan a trip" without destination, duration, or preferences)
    - If there are conflicting or contradictory requirements

    OUTPUT FORMAT:
    If you need clarification, output ONLY this JSON format:
    {
        "user_question": "Specific question to ask the user for clarification"
    }

    If you can evaluate the plan, output ONLY this JSON format:
    {
        "plan_feedback": "[Detailed step-by-step reasoning about the plan quality, feasibility, and completeness]",
        "tools_feedback": "[Analysis of tool selection, usage, and appropriateness for the task]",
        "score": [Integer between -100 and 100 reflecting plan quality]
    }

    SCORING GUIDELINES:
    - 80-100: Excellent plan that fully addresses the problem with optimal tool usage
    - 60-79: Good plan with minor issues or inefficiencies
    - 40-59: Adequate plan but with notable gaps or suboptimal approaches
    - 20-39: Poor plan with significant issues but some correct elements
    - 0-19: Very poor plan with major flaws
    - Below 0: Fundamentally flawed or completely inappropriate plan

    """

    return prompt.replace("{problem}", problem).replace("{plan}", str(plan)).replace("{constraints}", str(constraints))


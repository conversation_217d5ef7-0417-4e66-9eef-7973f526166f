



def get_verifier_prompt(problem, plan, constraints, clarification_history=None):

    # Build clarification history section
    clarification_section = ""
    if clarification_history and len(clarification_history) > 0:
        clarification_section = "\n\nPREVIOUS CLARIFICATIONS:\n"
        clarification_section += "The following questions have already been asked and answered:\n"
        for i, qa in enumerate(clarification_history, 1):
            clarification_section += f"{i}. Q: {qa['question']}\n   A: {qa['answer']}\n"
        clarification_section += "\nDO NOT repeat any of these questions. Ask for different clarifications if needed.\n"

    prompt = """
    You are an expert plan verifier. Your task is to evaluate the quality of the provided plan and provide feedback.

    Problem Statement:
    {problem}

    Plan:
    {plan}

    Constraints:
    {constraints}""" + clarification_section + """

    EVALUATION INSTRUCTIONS:
    1. First, determine if you have enough information to properly evaluate this plan
    2. If the problem statement is vague, ambiguous, or missing critical details needed for planning, ask for clarification
    3. Otherwise, provide a detailed evaluation with a score

    WHEN TO ASK FOR CLARIFICATION:
    - If the problem lacks specific dates, locations, or requirements
    - If budget constraints are mentioned but not specified
    - If the scope is unclear (e.g., "plan a trip" without destination, duration, or preferences)
    - If there are conflicting or contradictory requirements

    IMPORTANT: If previous clarifications are shown above, DO NOT ask the same or similar questions again.
    Only ask for NEW information that hasn't been covered in previous clarifications.

    OUTPUT FORMAT:
    If you need clarification, output ONLY this JSON format:
    {{
        "user_question": "Specific question to ask the user for clarification"
    }}

    If you can evaluate the plan, output ONLY this JSON format:
    {{
        "plan_feedback": "[Detailed step-by-step reasoning about the plan quality, feasibility, and completeness]",
        "tools_feedback": "[Analysis of tool selection, usage, and appropriateness for the task]",
        "score": [Integer between -100 and 100 reflecting plan quality]
    }}

    SCORING GUIDELINES:
    - 80-100: Excellent plan that fully addresses the problem with optimal tool usage
    - 60-79: Good plan with minor issues or inefficiencies
    - 40-59: Adequate plan but with notable gaps or suboptimal approaches
    - 20-39: Poor plan with significant issues but some correct elements
    - 0-19: Very poor plan with major flaws
    - Below 0: Fundamentally flawed or completely inappropriate plan

    """

    return prompt.replace("{problem}", problem).replace("{plan}", str(plan)).replace("{constraints}", str(constraints))


import asyncio
import json
import re
import logging
import time
from llm import LLM
from pprint import pprint
from prompts.tool_selector_prompt import get_tool_selector_prompt, tools




class ToolSelector:
    def __init__(self, llm: LLM):
        self.llm = llm

    async def select_tools(self, query: str, feedback=None, selected_tools=None, model="o4-mini"):
        prompt = get_tool_selector_prompt(tools, query, feedback=feedback, selected_tools=selected_tools)
        messages = [
            # {"role": "system", "content": "You are an expert tool selector that selects the most relevant tools for a user's request."},
            {"role": "user", "content": prompt}
        ]
        # pprint(messages)
        # exit()
        start_time = time.time()
        response = await self.llm.generate(messages, model=model, temperature=0.0)
        end_time = time.time()
        print(f"Time taken: {end_time - start_time} seconds")
        return response

    def _extract_tool_ids(self, response: str) -> list[int]:
        """Extract a list of tool IDs from an LLM response string.

        The LLM is expected to return a JSON object like {"tool_IDs": [1, 2]}. However, the
        response can be wrapped in markdown code-fences or contain additional text.
        """
        cleaned = response.strip()
        if cleaned.startswith("```"):
            # Strip the first line (``` or ```json) and the trailing ```
            cleaned = re.sub(r"^```[a-zA-Z0-9]*\n", "", cleaned)
            cleaned = cleaned.rstrip("`").rstrip()
        # Find the first JSON object
        match = re.search(r"\{[\s\S]*?\}", cleaned)
        if not match:
            return []
        json_str = match.group(0)
        data = json.loads(json_str)
        if isinstance(data, dict) and "tool_IDs" in data and isinstance(data["tool_IDs"], list):
            return [int(x) for x in data["tool_IDs"] if isinstance(x, int)]
        return []

    async def select_tools_batch(self, query: str, batch_size: int = 5, model: str = "o4-mini") -> dict:
        """Select relevant tools using parallel batched prompting.

        Args:
            query:        The user request.
            batch_size:   Number of tools to include in each prompt batch. Default is 5.
            model:        The model name to pass through to the underlying LLM.

        Returns:
            A dictionary of the form {"tool_IDs": [...]} containing all IDs selected across
            every batch, ordered according to the original *global* tool list order.
        """
        if batch_size <= 0:
            raise ValueError("batch_size must be a positive integer")

        # Split the master tool list into chunks of size batch_size
        tool_batches: list[list[dict]] = [tools[i:i + batch_size] for i in range(0, len(tools), batch_size)]

        # Prepare coroutine tasks – one generate() call per batch
        coroutines = []
        for batch in tool_batches:
            batch_prompt = get_tool_selector_prompt(batch, query)
            messages = [{"role": "user", "content": batch_prompt}]
            coroutines.append(self.llm.generate(messages, model=model))

        # Run all batches concurrently
        start_time = time.time()
        responses = await asyncio.gather(*coroutines, return_exceptions=True)
        end_time = time.time()
        print(f"Batch tool selection completed in {end_time - start_time} seconds")

        # Collect IDs from every batch
        selected_ids: set[int] = set()
        for resp in responses:
            # Skip failed responses (exceptions are returned when return_exceptions=True)
            if isinstance(resp, Exception):
                logging.error("Tool batch selection failed: %s", resp)
                continue
            selected_ids.update(self._extract_tool_ids(str(resp)))

        # Preserve original global ordering
        ordered_ids = [tool["id"] for tool in tools if tool["id"] in selected_ids]
        return {"tool_IDs": ordered_ids}



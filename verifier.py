import asyncio
import json
import re
import logging
import time
import traceback
from llm import LLM
from pprint import pprint
from prompts.verifier_prompt import get_verifier_prompt




class ToolSelector:
    def __init__(self, llm: LLM):
        self.llm = llm

    def _extract_json_from_response(self, response: str):
        """Extract JSON from LLM response, handling both user_question and feedback formats."""
        cleaned = response.strip()
        if cleaned.startswith("```"):
            cleaned = re.sub(r"^```[a-zA-Z0-9]*\n", "", cleaned)
            cleaned = cleaned.rstrip("`").rstrip()

        # Find the first JSON object
        match = re.search(r"\{[\s\S]*?\}", cleaned)
        if match:
            try:
                return json.loads(match.group(0))
            except json.JSONDecodeError as e:
                logging.error(f"Failed to parse JSON from verifier response: {e}")
                return {}
        return {}

    def _ask_user_for_clarification(self, question: str) -> str:
        """Ask the user for clarification using input() function."""
        print(f"\n{'-'*60}")
        print("🤔 CLARIFICATION NEEDED")
        print(f"{'-'*60}")
        print(f"Question: {question}")
        print(f"{'-'*60}")

        user_response = input("Your response: ").strip()

        print(f"{'-'*60}")
        print("✅ Thank you for the clarification!")
        print(f"{'-'*60}\n")

        return user_response

    async def verify_plan(self, query, plan, constraints, model="o4-mini", max_clarification_rounds=3):
        """
        Verify a plan and handle user clarification requests.

        Args:
            query: The original user query
            plan: The plan to verify
            constraints: The constraints to consider
            model: The model to use for verification
            max_clarification_rounds: Maximum number of clarification rounds to prevent infinite loops

        Returns:
            The verification response (either feedback JSON or final result after clarifications)
        """
        original_query = query
        clarification_count = 0
        clarification_history = []  # Track previous Q&A pairs

        while clarification_count < max_clarification_rounds:
            # Build the prompt with clarification history
            prompt = get_verifier_prompt(query, plan, constraints, clarification_history)
            messages = [
                {"role": "user", "content": prompt}
            ]

            start_time = time.time()
            response = await self.llm.generate(messages, model=model, temperature=0.0)
            print(response)
            print('$$$$$$$$$$$$$$$$$$$$$')
            print('$$$$$$$$$$$$$$$$$$$$$')
            print('$$$$$$$$$$$$$$$$$$$$$')
            print('$$$$$$$$$$$$$$$$$$$$$')
            print('$$$$$$$$$$$$$$$$$$$$$')
            print('$$$$$$$$$$$$$$$$$$$$$')
            print('$$$$$$$$$$$$$$$$$$$$$')
            print('$$$$$$$$$$$$$$$$$$$$$')
            print('$$$$$$$$$$$$$$$$$$$$$')
            print('$$$$$$$$$$$$$$$$$$$$$')
            end_time = time.time()
            print(f"Time taken: {end_time - start_time} seconds")

            # Try to extract JSON from response
            response_json = self._extract_json_from_response(response)

            # Check if this is a user question
            if "user_question" in response_json:
                clarification_count += 1
                question = response_json["user_question"]

                # Check if this question was already asked
                if any(qa["question"].lower().strip() == question.lower().strip() for qa in clarification_history):
                    print(f"\n⚠️  This question was already asked. Skipping to avoid repetition.")
                    print(f"Question: {question}")
                    continue

                print(f"\n{'-'*80}")
                print(f"CLARIFICATION REQUEST (Round {clarification_count}/{max_clarification_rounds})")
                print(f"{'-'*80}")

                user_answer = self._ask_user_for_clarification(question)

                # Add to clarification history
                clarification_history.append({
                    "question": question,
                    "answer": user_answer
                })

                # Update the query with all clarifications
                clarifications_text = "\n".join([
                    f"Q: {qa['question']}\nA: {qa['answer']}"
                    for qa in clarification_history
                ])
                query = f"{original_query}\n\nPrevious clarifications:\n{clarifications_text}"

                print(f"Updated query with clarification history")
                continue

            # If we have a score, this is the final verification response
            elif "score" in response_json or any(key in response_json for key in ["plan_feedback", "tools_feedback"]):
                return response

            # If we can't parse the response properly, return it as-is
            else:
                logging.warning("Could not parse verifier response as either user_question or feedback format")
                return response

        # If we've exceeded max clarification rounds, return a default response
        print(f"\n⚠️  Maximum clarification rounds ({max_clarification_rounds}) reached. Proceeding with current information.")
        return '{"plan_feedback": "Maximum clarification rounds reached. Proceeding with available information.", "tools_feedback": "Unable to get sufficient clarification from user.", "score": -50}'


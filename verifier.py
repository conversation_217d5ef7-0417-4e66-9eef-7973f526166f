import asyncio
import json
import re
import logging
import time
import traceback
from llm import LLM
from pprint import pprint
from prompts.verifier_prompt import get_verifier_prompt




class ToolSelector:
    def __init__(self, llm: LLM):
        self.llm = llm

    async def verify_plan(self, query, plan, constraints, model="o4-mini"):
        prompt = get_verifier_prompt(query, plan, constraints)
        messages = [
            # {"role": "system", "content": "You are an expert tool selector that selects the most relevant tools for a user's request."},
            {"role": "user", "content": prompt}
        ]
        # pprint(messages)
        # exit()
        start_time = time.time()
        response = await self.llm.generate(messages, model=model, temperature=0.0)
        end_time = time.time()
        print(f"Time taken: {end_time - start_time} seconds")
        return response

